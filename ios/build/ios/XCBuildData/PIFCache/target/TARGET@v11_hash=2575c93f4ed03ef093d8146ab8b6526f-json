{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b704de715ef91e375da34eaaa9326c3f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9b0e42eddf5ddd252a4c7a5173f7576", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9b0e42eddf5ddd252a4c7a5173f7576", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bce762d9a0d5820e0bb3b69fe4dee5e", "guid": "bfdfe7dc352907fc980b868725387e98be2c41c899d1273e6d632a56ba8d7e17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825eb62dff77912fe4610020ddb9e80d5", "guid": "bfdfe7dc352907fc980b868725387e9851643e713658d14d29d6829782cbb4b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f72cf36efbb499f11c84258f41a44c86", "guid": "bfdfe7dc352907fc980b868725387e9817ba561fb18b74dd35e69503796de8d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4832704431b51c0c2cff032dba7dbab", "guid": "bfdfe7dc352907fc980b868725387e98e24f185acfcc7a37dc0388418630035d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebab78f71d521d3fa2bfd301ffb850c6", "guid": "bfdfe7dc352907fc980b868725387e986fb04a840407641caacced9b7afd0e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98673c84f9248f92a28b4fc3b596835f46", "guid": "bfdfe7dc352907fc980b868725387e989e7a155999504dc0a9f264b88821207c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4da9c8a2f48c5779ccdac1872fe774b", "guid": "bfdfe7dc352907fc980b868725387e986e93e86c54e81b4099ac32b2f80331e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989efe61c363dee7d9b3f24783f30e7210", "guid": "bfdfe7dc352907fc980b868725387e9892de17e91461b157d2ff868c71245a5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f38672c2547f54ab198d871792d67d74", "guid": "bfdfe7dc352907fc980b868725387e98ccc08afea492b4a1e584294bb007c896", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f18af824bcaf6fc62e260a3a4e1e6a7", "guid": "bfdfe7dc352907fc980b868725387e983521b7cd1f6a9460255380c2e8bd3a76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e2f99568f2cd25d6beb45f8c2c03f9", "guid": "bfdfe7dc352907fc980b868725387e9806f3c8f52df7d5d86b35f27ec804833b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b37c6fbe0dc35a7d9884e23e003218", "guid": "bfdfe7dc352907fc980b868725387e980481fd222799c6dbbb547e04619399d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e311806865a62c06395c14072925acb", "guid": "bfdfe7dc352907fc980b868725387e986628c4eb4f0c01134872824bd0b57754", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98990c99abcc09ffc3eb4bdad2956f5a49", "guid": "bfdfe7dc352907fc980b868725387e981ffda07ad10fa61f9d8343f9e5802258", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893763ef668c3fdbf1cad8d91f0eb40ad", "guid": "bfdfe7dc352907fc980b868725387e9884a940986487d110356e4ea121ddad2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e23ba29542493b34e67b00198467599", "guid": "bfdfe7dc352907fc980b868725387e98a035677a5f5af6b2626b66c3934bc772", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fa3634be77a1bab3dfe2140b16c1b01", "guid": "bfdfe7dc352907fc980b868725387e980133e3458c89bb95849d196600f559e4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98accd8fed785d85a5944452e21290b37b", "guid": "bfdfe7dc352907fc980b868725387e9869a01b04319795d3ba134280e5b3aad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804c2ef1752877171ce2ac5eb20a1ccd0", "guid": "bfdfe7dc352907fc980b868725387e9887a3f9fbc3dc4cf61ce3bfd34cb48fc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1473c4158498584ae99ccba2c6309bb", "guid": "bfdfe7dc352907fc980b868725387e985af5e7f6beaafbbd4b695fc9b31294b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f390af789cf5901475e319c85aa9710", "guid": "bfdfe7dc352907fc980b868725387e98fd5c5c15ff3e26167ba0a16259e0280d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815bc36e01ffef630d675dc439d25c276", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f152bf9a42438e18a623b7d216df97c", "guid": "bfdfe7dc352907fc980b868725387e98e69ee1ca0e2fa73c4851dfd4917080c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989480eb883f0547bc354a67c5a8eac6aa", "guid": "bfdfe7dc352907fc980b868725387e98d441388c343649dace73588215b70832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f12e5456b9e6192c03a7e9c00404da63", "guid": "bfdfe7dc352907fc980b868725387e98caaf5201fdfbf16ea5aee50b6acc5e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0923d948cf9e8e1ab2e14f58a68ba98", "guid": "bfdfe7dc352907fc980b868725387e98bb6368b1c4ab73b09a96040d1bbd3697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832293d3a38008d17a9206e0bbea42f9c", "guid": "bfdfe7dc352907fc980b868725387e984069ad8f4dd463849cd55f546f840132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98160ffe4c7d4bc6b6b8095a712849f92c", "guid": "bfdfe7dc352907fc980b868725387e98d9dcbf389b3d1b5a507d5d60a21b0c28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981af35df079540836a38be5a0afd7a452", "guid": "bfdfe7dc352907fc980b868725387e98a95cab8f4d69c86ff793564af7099191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba5f71eb1367e24003646a05a04df844", "guid": "bfdfe7dc352907fc980b868725387e98c60c36d6b388d64b429d0a4e5aaf86dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbfd7960303791c1d716f02dfe45f958", "guid": "bfdfe7dc352907fc980b868725387e98888657137765bca507e1b4362cfde572"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}