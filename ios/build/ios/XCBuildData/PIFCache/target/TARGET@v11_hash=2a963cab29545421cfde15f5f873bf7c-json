{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98112e40140b88d409c7523854646791b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e6d139ce8039ef5366bce9c11f6dbb4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984e6d139ce8039ef5366bce9c11f6dbb4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98451a3b889747c6b84e883ed93b6f26b5", "guid": "bfdfe7dc352907fc980b868725387e98af20d40680c4d3a233b62094331148df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cd2bb962160d569479e3ec9e716e209", "guid": "bfdfe7dc352907fc980b868725387e9883499075bf7b67aecf83a9e47c313234", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ac1df6f5c4e52157acd5f19f57a7334", "guid": "bfdfe7dc352907fc980b868725387e98ba8695d62926a1e556c112d8d164c7c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc19c1813d508d739f20da7bc9e96f4d", "guid": "bfdfe7dc352907fc980b868725387e9810516dc21a956740e45b617b90471c0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98074e68d5e064f83290607f2f3e808e30", "guid": "bfdfe7dc352907fc980b868725387e981e0a6eb4d2de87c58c4b40122c91124b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ba2bfff252ebb4a97496167ce83835b", "guid": "bfdfe7dc352907fc980b868725387e980c26ff6c868e7ac859488b18d7c156d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988237a5be299e5af68a8a7ab46d65b5d5", "guid": "bfdfe7dc352907fc980b868725387e98090753897f45737b4c96548267a4a319", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801ef0463f91e1bd65a48275e00236ae9", "guid": "bfdfe7dc352907fc980b868725387e98dd76cd4644a216ec3086f8fe9d1d13db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccfefc92b6b3a06f9feb4bb4c2f1fe13", "guid": "bfdfe7dc352907fc980b868725387e982eb8e8e92f13bba3e6b2f30a00de3a77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bdde77350f4dee7e1580c8cb6f74e6c", "guid": "bfdfe7dc352907fc980b868725387e9876153e0946f4cc7b4ab40ce0e836a9e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867c88076eadba272c6428d5fd712ce8b", "guid": "bfdfe7dc352907fc980b868725387e98c5be93c83f8b50f6c0dad0b83255f9f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98950ceca2ccc0e15f4473fd7532562b61", "guid": "bfdfe7dc352907fc980b868725387e983768becebed0c42662ceaf141d837b7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1ae4bb2f64095c09d61bbff1b4cfb7e", "guid": "bfdfe7dc352907fc980b868725387e98b3b44dd34956af6d9b6a411f7f3768ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a735287e8251bbf014d8b20f2c013e", "guid": "bfdfe7dc352907fc980b868725387e9819e2699a65dceb32f18d723b7dbc0f60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac07a6d872e041ef87fe7dd74dd4906b", "guid": "bfdfe7dc352907fc980b868725387e98ba44c1cb45b7f6e3fccf8b9172b7e15c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afc626a1195adfe3e04759e779d4c134", "guid": "bfdfe7dc352907fc980b868725387e9890f6be5f3da5b813003a7238f86e03db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3b2941d1a13cce712704d37724f7b40", "guid": "bfdfe7dc352907fc980b868725387e981b81b73f27e83ca62e76bf3fe1ba68f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815ae2c9f5d7451eb2b8d80b9dd2413f7", "guid": "bfdfe7dc352907fc980b868725387e98cb8f07efadfffb1090f065b31846d8cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7939006669c2b04f29d2150ae957121", "guid": "bfdfe7dc352907fc980b868725387e98938f638f4432c7548839d1c060b2cfcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888145dfad63e2017f1d38a8216e7fa92", "guid": "bfdfe7dc352907fc980b868725387e9882bf7914ba75751dfb7f6a0f46c93c2d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983bcd4f46eb41a0295b6b350c5065a96c", "guid": "bfdfe7dc352907fc980b868725387e98355830aedfa37710c9b3aedc667e3721"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6f5d04f8778e57d0241029aaeef4b3b", "guid": "bfdfe7dc352907fc980b868725387e989f67061233e634256675973f3e892dd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839cc4918a8be06458681c84d0a5b4fe1", "guid": "bfdfe7dc352907fc980b868725387e98266ecc11b3fe3e60c833605748792dbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c01a6cfc9e9d79947cfb710445f6ba9f", "guid": "bfdfe7dc352907fc980b868725387e98312f8e6d64ef5205828264f07b9b32db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345e532d77da3202d88ce8f7b4b6eb06", "guid": "bfdfe7dc352907fc980b868725387e988f61bd9001f24511dfb47106e8cf4ede"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd09ac0469fe5ca7c7aecd888588162", "guid": "bfdfe7dc352907fc980b868725387e98ab89814c225bae4efb9dc80c2a56b81a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd754c3fa9e282fa2b97e1db33953183", "guid": "bfdfe7dc352907fc980b868725387e9824d8819a58d704a61c1231be335e652d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811e142631775044586e1a6b0657d825c", "guid": "bfdfe7dc352907fc980b868725387e987adc3a0f858d13941e4d63ba1eb9c769"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d11565a7aeb820545a57dab396a2f52", "guid": "bfdfe7dc352907fc980b868725387e98521538bcc9371849667320a1b4d3eed9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98330adbb064caf01a67c7bc3792094e89", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ef765cb0c38584475c97a0a86884f78", "guid": "bfdfe7dc352907fc980b868725387e9866bd70a6af000e5c227d01efd58e9280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc4c71ecb05a4e811d3e6d4efc9ef236", "guid": "bfdfe7dc352907fc980b868725387e9876f96956c4e6ea6a7a77ffbce7801e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d43065c094eb70b2c5fdc3b15acb77c3", "guid": "bfdfe7dc352907fc980b868725387e98ab8b523a20c1decfcfb8a9b263155bcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e191e3a43fd8d72b4c1a439641bf4233", "guid": "bfdfe7dc352907fc980b868725387e983ca393e68b94b6c8e8feb9cb8b55c251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841337bfccf34bbe9eb349f9dca58666e", "guid": "bfdfe7dc352907fc980b868725387e988288276c5d57d49ef8d760e5e646a621"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1844ae7a15dd2999a849ffa12f9a881", "guid": "bfdfe7dc352907fc980b868725387e9806311884d3a607cccfe45e87b56e6a53"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}