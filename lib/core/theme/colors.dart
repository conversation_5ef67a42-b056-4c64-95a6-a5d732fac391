import '../imports/core_imports.dart';

class AppColors {
  static const Color primaryColor = Color(0xFF41D5FB);
  static const Color hintColor = Color(0xFFBDBDBD);
  static const Color lineColor = Color(0xFFEAECF0);

  static const Color secondaryColor = Color(0xFFFFFFFF);
  static const Color tertiaryColor = Color(0xFF39D2C0);
  static const Color lightPrimaryBackgroundColor = Color(0xFFFFFFFF);
  static const Color divider = Color(0xFFF5F5F5);
  static const Color lightSecondaryBackgroundColor = Color(0xFFF1F4F8);
  static const Color whiteColor = Color(0xFFFFFFFF);
  static const Color darkPrimaryBackgroundColor = Color(0xFF1A1F24);
  static const Color borderColor = Color(0xff268eff99);

  static const Color unSelectedButton = Color(0xFFF8F8F8);
  static const Color primaryGreyColor = Color(0xFF333333);
  static const Color primaryHintColor = Color(0xFFFEFEFE);
  static const Color primaryBorderColor = Color(0xFF268EFF);

  static const Color lightGreyColor = Color(0xFFBDBDBD);
  static const Color lightestGreyColor = Color(0xFFE0E0E0);

  static const Color successColor = Color(0xFF23AF33);
  static const Color errorColor = Color(0xFFE74852);
  static const Color warningColor = Color(0xFFFF8200);

  static const Color greyBorderColor = Color(0xFFD9D9D9);

  static const Color textGreyColor = Color(0xFF828282);

  static const Color greyCardColor = Color(0xFFF3F3F3);

  static const Color lightgreenCardColor = Color(0xFFD8ECF1);

  static const Color blackTextColor = Color(0xFF1D1D1D);
  static const Color blackColor = Color(0xFF000000);

  static const Color yellowColor = Color(0xFFFFBC00);
  static const Color blueRowColor = Color(0xffc1dfff3d);
  static const Color texGreyColor = Color(0xFF333333);
  static const Color texLightBlackColor = Color(0xD9000000);
  static const Color selectionColor = Color(0x81268EFF);
  static const Color selectionMealPortion = Color(0x1A268EFF);
  static const Color selctBoarderColor = Color(0xFFE0E0E0);

  static const Color darkGreyColor = Color(0xFF747474);
  static const Color linghtTextGreyColor = Color(0xFF667085);
  static const Color varientBlueColor = Color(0xFF488AC8);





  static const Color primaryRedColor = Color(0xFFE53935); // Example red color







}
