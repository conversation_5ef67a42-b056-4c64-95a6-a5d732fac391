import 'package:flutter/material.dart';

class GradientBackground extends StatelessWidget {
  // Optional child widget to display on top of the gradient.
  final Widget? child;

  // List of colors for the gradient.
  final List<Color> colors;

  // Constructor that requires a list of colors and an optional child.
  const GradientBackground({
    Key? key,
    required this.colors,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // The Container is configured to fill the entire available space.
      width: double.infinity,
      height: double.infinity,
      // The BoxDecoration provides the gradient.
      decoration: BoxDecoration(
        gradient: LinearGradient(
          // Gradient starts at the top-left.
          begin: Alignment.topCenter,
          // Gradient ends at the bottom-right.
          end: Alignment.bottomRight,
          // The colors are provided by the user of the widget.
          colors: colors,
        ),
      ),
      // The child widget is placed inside the Container, on top of the gradient.
      child: child,
    );
  }
}