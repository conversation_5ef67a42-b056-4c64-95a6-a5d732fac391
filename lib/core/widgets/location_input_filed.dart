import 'package:flutter/material.dart';
import 'package:towchain_customer_app/core/extensions/context_extensions.dart';
import '../../presentation/screens/findService/subViews/drop_off_bottom_sheet.dart';
import '../theme/colors.dart';
// Assuming DropOffBottomSheet is in the same directory or a known path

class LocationInputField extends StatelessWidget {
  final String hintText;
  final bool readOnly; // New parameter
  final VoidCallback? onTap; //

  const LocationInputField({
    super.key,
    required this.hintText,
    this.readOnly = false, // Default to false
    this.onTap, // Optional onTa
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(
          color: AppColors.hintColor,
          fontSize: 14,
          fontFamily: "Inter-Regular",
          fontWeight: FontWeight.w400,
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 12,
          horizontal: 12,
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppColors.primaryBorderColor,
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: AppColors.lightestGreyColor,
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: AppColors.primaryBorderColor,
            width: 1,
          ),
        ),
        isDense: true,
      ),
    );
  }
}