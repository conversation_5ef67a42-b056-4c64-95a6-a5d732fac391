
// A common AppBar widget to be reused across different screens.
// It allows for flexible leading and actions widgets.
import 'package:towchain_customer_app/core/widgets/custom_text.dart';

import '../imports/core_imports.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Widget? leading;
  final List<Widget>? actions;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.leading,
    this.actions,
  }) : super(key: key);

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: CustomText(text: title,fontFamily: 'Urbanist-SemiBold',size: 18,weight: FontWeight.w600,color: AppColors.whiteColor),
      leading: leading,
      actions: actions,
      backgroundColor: Colors.black, // Dark background color
      elevation: 0, // No shadow
    );
  }
}

