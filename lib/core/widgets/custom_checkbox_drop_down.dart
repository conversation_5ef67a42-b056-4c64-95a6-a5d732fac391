import 'package:flutter/material.dart';

import '../theme/colors.dart';
import 'custom_text.dart';

class CustomCheckBoxDropDownMobile<T> extends StatefulWidget {
  final String? label;
  final String? value;
  final List<T> options;
  final ValueChanged<String?> onChanged;
  final String Function(T) getDisplayString;
  final List<String> selectedValues;
  final ValueChanged<List<String>> onSelectionChanged;

  const CustomCheckBoxDropDownMobile({
    super.key,
    this.label,
    required this.value,
    required this.options,
    required this.onChanged,
    required this.getDisplayString,
    required this.selectedValues,
    required this.onSelectionChanged,
  });

  @override
  _CustomCheckBoxDropDownMobileState<T> createState() => _CustomCheckBoxDropDownMobileState<T>();
}

class _CustomCheckBoxDropDownMobileState<T> extends State<CustomCheckBoxDropDownMobile<T>> {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return PopupMenuButton<String>(
          // This onSelected is not used for multi-select, as the state
          // is managed within the PopMenuButton's items.
          onSelected: (String? newValue) {},
          itemBuilder: (context) {
            return widget.options.map((option) {
              final displayString = widget.getDisplayString(option);
              final isSelected = widget.selectedValues.contains(displayString);

              return PopupMenuItem<String>(
                value: displayString,
                // Using a StateBuilder to update the checkbox state immediately.
                // The PopupMenuItem's child now handles all logic.
                child: StatefulBuilder(
                  builder: (BuildContext context, StateSetter setState) {
                    return Container(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Checkbox(
                                value: isSelected,
                                onChanged: (bool? newValue) {
                                  // This is the correct place to handle the tap.
                                  setState(() {
                                    if (newValue == true) {
                                      if (!widget.selectedValues.contains(displayString)) {
                                        widget.selectedValues.add(displayString);
                                      }
                                    } else {
                                      widget.selectedValues.remove(displayString);
                                    }
                                    // Notify the parent widget of the change.
                                    widget.onSelectionChanged(widget.selectedValues);
                                  });
                                },
                                activeColor: AppColors.primaryColor,
                              ),
                              CustomText(
                                text: displayString,
                                size: 12,
                                weight: isSelected
                                    ? FontWeight.w600
                                    : FontWeight.w400,
                                color: isSelected
                                    ? AppColors.primaryColor
                                    : AppColors.primaryGreyColor,
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
              );
            }).toList();
          },
          color: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          constraints: BoxConstraints(minWidth: constraints.maxWidth),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.lightestGreyColor),
              borderRadius: BorderRadius.circular(8.0),
              color: AppColors.whiteColor,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: CustomText(
                    text: widget.selectedValues.isEmpty
                        ? 'Select'
                        : widget.selectedValues.join(', '),
                    color: AppColors.primaryGreyColor,
                    weight: FontWeight.w400,
                    size: 12,
                    maxLine: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 5),
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 18,
                    color: AppColors.blackColor,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}