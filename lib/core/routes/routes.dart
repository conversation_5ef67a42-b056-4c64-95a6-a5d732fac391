

import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:towchain_customer_app/core/routes/routing_paths.dart';
import 'package:towchain_customer_app/presentation/screens/auth/view/enable_location_screen.dart';
import 'package:towchain_customer_app/presentation/screens/auth/view/forgot_password_screen.dart';
import 'package:towchain_customer_app/presentation/screens/auth/view/on_board_screen.dart';
import 'package:towchain_customer_app/presentation/screens/auth/view/verify_otp_screen.dart';
import 'package:towchain_customer_app/presentation/screens/findService/add_service_faq.dart';
import 'package:towchain_customer_app/presentation/screens/findService/find_service_details_screen.dart';
import 'package:towchain_customer_app/presentation/screens/findService/find_service_preview.dart';
import 'package:towchain_customer_app/presentation/screens/findService/select_category_screen.dart';
import 'package:towchain_customer_app/presentation/screens/findService/select_service_type.dart';
import 'package:towchain_customer_app/presentation/screens/findService/select_vehicle_type.dart';
import 'package:towchain_customer_app/presentation/screens/findService/set_location_on_map.dart';
import 'package:towchain_customer_app/presentation/screens/home/<USER>';
import 'package:towchain_customer_app/presentation/screens/paymentAndRating/completed_payment_details_screen.dart';
import 'package:towchain_customer_app/presentation/screens/paymentAndRating/service_done_screen.dart';
import 'package:towchain_customer_app/presentation/screens/selectService/payment_details_screen.dart';

import '../../presentation/screens/auth/view/login_screen.dart';
import '../../presentation/screens/auth/view/sign_up_screen.dart';
import '../../presentation/screens/auth/view/splash_screen.dart';
import '../imports/core_imports.dart';

final rootNavigatorKey = GlobalKey<NavigatorState>();
final shellNavigatorKey = GlobalKey<NavigatorState>();

/// This will be replaced with a real auth provider

final goRouterProvider = Provider<GoRouter>((ref) {

  return GoRouter(
    navigatorKey: rootNavigatorKey,
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    routes: [
      /// Splash Screen
      GoRoute(
        path: Routes.splash,
        builder: (context, state) => SplashScreen(),
      ),

      GoRoute(
        path: Routes.onboard,
        builder: (context, state) =>  OnBoardScreen(),
      ),
      /// Login Screen
      GoRoute(
        path: Routes.login,
        builder: (context, state) => const LoginScreen(),
      ),

      /// SignUp Screen
      GoRoute(
        path: Routes.signup,
        builder: (context, state) => const SignupScreen(),
      ),
      //
      /// Mobile Account Settings Screen
      GoRoute(
        path: Routes.forgotPassword,
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      /// Mobile Subscription Screen
      GoRoute(
        path: Routes.verifyOtp,
        builder: (context, state) => const VerifyOtpScreen(),
      ),
      GoRoute(
        path: Routes.enableLocation,
        builder: (context, state) => const EnableLocationScreen(),
      ),
      GoRoute(
        path: Routes.home,
        builder: (context, state) => const HomeScreen(),
      ),

      GoRoute(
        path: Routes.selectVehicleType,
        builder: (context, state) => const SelectVehicleTypeScreen(),
      ),
      GoRoute(
        path: Routes.selectServiceType,
        builder: (context, state) => const SelectServiceTypeScreen(),
      ),

      GoRoute(
        path: Routes.selectCategoryType,
        builder: (context, state) => const SelectCategoryScreen(),
      ),
      GoRoute(
        path: Routes.findServiceDetailsScreen,
        builder: (context, state) => const FindServiceDetailsScreen(),
      ),
      GoRoute(
        path: Routes.setLocationOnMap,
        builder: (context, state) => const SetLocationOnMapScreen(),
      ),
      GoRoute(
        path: Routes.addServiceFaqScreen,
        builder: (context, state) => const AddServiceFaqScreen(),
      ),
      GoRoute(
        path: Routes.findServicePreviewScreen,
        builder: (context, state) => const FindServicePreviewScreen(),
      ),
      GoRoute(
        path: Routes.paymentDetailsScreen,
        builder: (context, state) => const PaymentDetailsScreen(),
      ),
      GoRoute(
        path: Routes.completesPaymentDetailsScreen,
        builder: (context, state) => const CompletedPaymentDetailsScreen(),
      ),

      GoRoute(
        path: Routes.confirmationScreen,
        builder: (context, state) => const ConfirmationScreen(),
      ),

      //
      // /// Change Password Mobile Screen
      // GoRoute(
      //   path: Routes.changePasswordMobile,
      //   parentNavigatorKey: rootNavigatorKey,
      //   builder: (context, state) => const ChangePasswordMobile(),
      // ),
      //
      // ...personalRoutes,
    ],
  );
});