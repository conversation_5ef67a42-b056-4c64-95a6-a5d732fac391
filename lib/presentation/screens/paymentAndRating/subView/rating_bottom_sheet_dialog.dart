import 'package:go_router/go_router.dart';

import '../../../../core/imports/core_imports.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_input_field.dart';
import '../../../../core/widgets/custom_text.dart';

class RatingBottomSheet extends StatefulWidget {
  const RatingBottomSheet({super.key});

  @override
  State<RatingBottomSheet> createState() => _RatingBottomSheetState();
}

class _RatingBottomSheetState extends State<RatingBottomSheet> {
  int _rating = 0;
  final TextEditingController _feedbackController = TextEditingController();

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          left: 24,
          right: 24,
          top: 24,
        ),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              height: 4,
              width: 80,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(5),
              ),
            ),
            const SizedBox(height: 16),
            const SizedBox(
              height: 80,
              width: 80,
              child: CircleAvatar(
                backgroundImage: AssetImage('assets/images/user.png'),
                radius: 30.0,
              ),
            ),
            const SizedBox(height: 8),
            const CustomText(
              text: 'David Morel',
              size: 14,
              weight: FontWeight.w600,
              color: AppColors.blackTextColor,
            ),
            const SizedBox(height: 16),
            const CustomText(
              text: 'We Value Your Feedback!',
              size: 20,
              weight: FontWeight.w600,
              color: AppColors.blackTextColor,
            ),
            const SizedBox(height: 8),
            const CustomText(
              text: 'Help us improve by sharing your experience.',
              size: 14,
              weight: FontWeight.w500,
              color: AppColors.lightGreyColor,
            ),
            const SizedBox(height: 16),
            const CustomText(
              text: 'How was your towing experience?',
              size: 14,
              weight: FontWeight.w500,
              color: AppColors.blackTextColor,
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) {
                return IconButton(
                  icon: Icon(
                    index < _rating ? Icons.star : Icons.star_border,
                    color: index < _rating ? Colors.amber : Colors.grey,
                    size: 40,
                  ),
                  onPressed: () {
                    setState(() {
                      _rating = index + 1;
                    });
                  },
                );
              }),
            ),
            const SizedBox(height: 16),
            const CustomText(
              text: 'Tell us more (optional):',
              size: 14,
              weight: FontWeight.w500,
              color: AppColors.blackTextColor,
            ),
            const CustomText(
              text: 'What went well or what could be better?',
              size: 14,
              weight: FontWeight.w500,
              color: AppColors.lightGreyColor,
            ),
            const SizedBox(height: 10),
            CustomInputField(
              hintText: '',
              fontSize: 14,
              controller: _feedbackController,
              keyboardType: TextInputType.text,
              maxLines: 3,
            ),
            const SizedBox(height: 24),
            // Corrected button layout
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    height: 55,
                    fontSize: 16,
                    text: "Skip",
                    weight: FontWeight.w700,
                    textColor: AppColors.blackColor,
                    color: AppColors.unSelectedButton,
                    onPressed: () {
                      context.push('/home');
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomButton(
                    height: 55,
                    fontSize: 16,
                    text: "Submit",
                    weight: FontWeight.w700,
                    textColor: AppColors.whiteColor,
                    color: AppColors.blackColor,
                    onPressed: () {
                      // Handle submit logic
                      print('Rating: $_rating');
                      print('Feedback: ${_feedbackController.text}');
                      context.push('/home');
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}