import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import '../../../core/assets_manager.dart';
import '../../../core/theme/colors.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class CompletedPaymentDetailsScreen extends StatefulWidget {
  const CompletedPaymentDetailsScreen({super.key});

  @override
  State<CompletedPaymentDetailsScreen> createState() =>
      _CompletedPaymentDetailsScreenState();
}

class _CompletedPaymentDetailsScreenState
    extends State<CompletedPaymentDetailsScreen> {
  bool _agreeToTerms = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'Payment',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              icon: SvgPicture.asset(
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              onPressed: () {
                context.pop(); // Uncomment for actual navigation
              },
            ),
          ),
        ),
      ),

      body: SingleChildScrollView(
        child: Column(
          children: [
            // Advance Payment Section
            Container(
              margin: const EdgeInsets.symmetric(
                horizontal: 10.0,
                vertical: 25.0,
              ),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomText(
                    text: 'Towing Service Completed!',
                    size: 16,
                    weight: FontWeight.w700,
                    color: AppColors.successColor,
                  ),

                  const SizedBox(height: 5),
                  CustomText(
                    text: 'Your total towing service fee',
                    size: 14,
                    weight: FontWeight.w500,
                    color: AppColors.linghtTextGreyColor,
                  ),

                  const SizedBox(height: 10),
                  const Center(
                    child: CustomText(
                      text: '\$90.00',
                      size: 24,
                      weight: FontWeight.w700,
                      color: AppColors.texGreyColor,
                    ),
                  ),
                ],
              ),
            ),
            Divider(thickness: 10, height: 10, color: AppColors.divider),

            // Details Section
            Container(
              padding: const EdgeInsets.symmetric(vertical: 20.0),

              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: CustomText(
                      text: 'Details',
                      size: 14,
                      weight: FontWeight.w700,
                      color: AppColors.texGreyColor,
                    ),
                  ),

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: const Divider(height: 30),
                  ),

                  // Profile Section
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: Row(
                      children: [
                        const CircleAvatar(
                          backgroundImage: AssetImage('assets/images/user.png'),
                          // Replace with your image asset
                          radius: 30.0,
                        ),
                        const SizedBox(width: 10),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomText(
                                text: 'David Morel',
                                size: 14,
                                weight: FontWeight.w600,
                                color: AppColors.blackTextColor,
                              ),
                              CustomText(
                                text: 'ID-12654',
                                size: 14,
                                weight: FontWeight.w500,
                                color: AppColors.lightGreyColor,
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8.0,
                            vertical: 4.0,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.yellowColor,
                            borderRadius: BorderRadius.circular(5.0),
                          ),
                          child: const Row(
                            children: [
                              CustomText(
                                text: '4.5',
                                size: 14,
                                weight: FontWeight.w600,
                                color: AppColors.texGreyColor,
                              ),

                              Icon(Icons.star, size: 16.0, color: Colors.black),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: const Divider(height: 30),
                  ),
                  // Pick Up and Drop Off Locations
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          text: 'PICK UP',
                          size: 12,
                          weight: FontWeight.w600,
                          color: AppColors.lightGreyColor,
                        ),

                        SizedBox(height: 5),
                        CustomText(
                          text: 'Westheimer Rd.Santa Ana. Illinois 85486',
                          size: 14,
                          weight: FontWeight.w500,
                          color: AppColors.texGreyColor,
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          text: 'DROP OFF',
                          size: 12,
                          weight: FontWeight.w600,
                          color: AppColors.lightGreyColor,
                        ),

                        SizedBox(height: 5),
                        CustomText(
                          text: 'Thornridge Cir. at 1901 Shiloh, Hawaii 81063',
                          size: 14,
                          weight: FontWeight.w500,
                          color: AppColors.texGreyColor,
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: const Divider(height: 30),
                  ),

                  // Price and Distance Details
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'PRICE',
                              size: 12,
                              weight: FontWeight.w600,
                              color: AppColors.lightGreyColor,
                            ),
                            CustomText(
                              text: '\$100.00',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.texGreyColor,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'MINIMUM PAYMENT ',
                              size: 12,
                              weight: FontWeight.w600,
                              color: AppColors.lightGreyColor,
                            ),
                            CustomText(
                              text: '-\$10.00',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.primaryRedColor,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'DISTANCE',
                              size: 12,
                              weight: FontWeight.w600,
                              color: AppColors.lightGreyColor,
                            ),
                            CustomText(
                              text: '2.5km',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.texGreyColor,
                            ),
                          ],
                        ),

                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'SERVICE',
                              size: 12,
                              weight: FontWeight.w600,
                              color: AppColors.lightGreyColor,
                            ),
                            CustomText(
                              text: 'Emergency Towing',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.texGreyColor,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: 25),

                  Divider(thickness: 10, height: 10, color: AppColors.divider),
                  SizedBox(height: 25),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: CustomText(
                      text: 'Additional Service',
                      size: 14,
                      weight: FontWeight.w700,
                      color: AppColors.texGreyColor,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: const Divider(height: 30),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'SERVICE NAME',
                              size: 12,
                              weight: FontWeight.w600,
                              color: AppColors.lightGreyColor,
                            ),
                            CustomText(
                              text: 'Car Wash',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.texGreyColor,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'SERVICE CATEGORY ',
                              size: 12,
                              weight: FontWeight.w600,
                              color: AppColors.lightGreyColor,
                            ),
                            CustomText(
                              text: 'Cleaning',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.texGreyColor,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'PRICE',
                              size: 12,
                              weight: FontWeight.w600,
                              color: AppColors.lightGreyColor,
                            ),
                            CustomText(
                              text: '\$150',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.texGreyColor,
                            ),
                          ],
                        ),

                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              text: 'HOUR',
                              size: 12,
                              weight: FontWeight.w600,
                              color: AppColors.lightGreyColor,
                            ),
                            CustomText(
                              text: '2h',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.texGreyColor,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),
                  Divider(thickness: 10, height: 10, color: AppColors.divider),
                  const SizedBox(height: 10),

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          text: 'TOTAL PAYMENT',
                          size: 12,
                          weight: FontWeight.w600,
                          color: AppColors.lightGreyColor,
                        ),
                        CustomText(
                          text: '\$240',
                          size: 14,
                          weight: FontWeight.w500,
                          color: AppColors.texGreyColor,
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15.0),
                    child: const Divider(height: 30),
                  ),
                  const SizedBox(height: 10),

                  _buildTermsAndConditionsCheckbox(),
                ],
              ),
            ),

            Container(
              padding: EdgeInsets.only(
                top: 40,
                left: 15,
                right: 15,
                bottom: 40,
              ),
              color: AppColors.divider,
              width: double.infinity,
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: CustomButton(
                        height: 55,
                        fontSize: 16,
                        text: "Pay Cash",
                        weight: FontWeight.w700,
                        textColor: AppColors.blackColor,
                        color: AppColors.unSelectedButton,
                        onPressed: () {
                          context.push('/confirmationScreen');
                        },
                      ),
                    ),
                  ),

                  // Continue / Get Started Button
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: CustomButton(
                        height: 55,
                        fontSize: 16,
                        // Change button text based on the current page
                        text: "Pay Online",
                        weight: FontWeight.w700,
                        textColor: AppColors.whiteColor,
                        color: AppColors.blackColor,
                        onPressed: () {
                          // Navigate to the home screen on the last page
                          context.push('/confirmationScreen');
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),


          ],
        ),
      ),

      // bottomNavigationBar:
    );
  }

  Widget _buildTermsAndConditionsCheckbox() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: Checkbox(
              value: _agreeToTerms,
              onChanged: (bool? value) {
                setState(() {
                  _agreeToTerms = value!;
                });
              },
              activeColor: AppColors.primaryColor,
              // Light blue
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
              side: const BorderSide(color: Colors.grey),
            ),
          ),
          const SizedBox(width: 8),
          const Expanded(
            child: Text.rich(
              TextSpan(
                text: 'I confirm that I have read and accept the Payment ',
                style: TextStyle(fontSize: 12.0),
                children: [
                  TextSpan(
                    text: 'Terms & Conditions',
                    style: TextStyle(
                      color: Colors.blue,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
