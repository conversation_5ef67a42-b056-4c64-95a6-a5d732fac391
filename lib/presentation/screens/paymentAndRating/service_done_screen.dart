import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:syncfusion_flutter_signaturepad/signaturepad.dart';
import 'package:towchain_customer_app/presentation/screens/paymentAndRating/subView/rating_bottom_sheet_dialog.dart';

import '../../../core/assets_manager.dart';
import '../../../core/imports/core_imports.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class ConfirmationScreen extends StatefulWidget {
  const ConfirmationScreen({super.key});

  @override
  State<ConfirmationScreen> createState() => _ConfirmationScreenState();
}

class _ConfirmationScreenState extends State<ConfirmationScreen> {
  final GlobalKey<SfSignaturePadState> signaturePadKey =
  GlobalKey<SfSignaturePadState>();
  bool isChecked = false;

  void handleClear() {
    signaturePadKey.currentState!.clear();
  }

  void handleSignAgain() {
    signaturePadKey.currentState!.clear();
  }

  void handleGenerateInvoice() {
    // Logic to handle invoice generation
    if (isChecked) {
      // Proceed with invoice generation
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Invoice generated!')));

      // This is the key part to fix
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        useRootNavigator: true, // <-- Add this line
        builder: (BuildContext context) {
          // Check the code for `RatingBottomSheet` to ensure it's valid
          return const RatingBottomSheet();
        },
      );
    } else {
      // Show error or warning
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please confirm service completion.')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'Service Done Confirmation',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              icon: SvgPicture.asset(
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              onPressed: () {
                context.pop(); // Uncomment for actual navigation
              },
            ),
          ),
        ),
      ),
      // Using a Builder widget to ensure the context is correct
      body: Builder(
        builder: (context) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CustomText(
                  text: 'Confirm Service Completion with Signatures',
                  size: 14,
                  weight: FontWeight.w700,
                  color: AppColors.texGreyColor,
                ),

                const Divider(height: 30),
                const CustomText(
                  text: 'Service Provider Signature',
                  size: 14,
                  weight: FontWeight.w700,
                  color: AppColors.texGreyColor,
                ),
                const CustomText(
                  text: 'David Morel',
                  size: 16,
                  weight: FontWeight.w500,
                  color: AppColors.linghtTextGreyColor,
                ),

                const SizedBox(height: 8),
                Container(
                  height: 400,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.lightestGreyColor),
                    borderRadius: BorderRadius.circular(10.0),
                    color: AppColors.whiteColor,
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SizedBox(
                          height: 280,
                          child: SfSignaturePad(
                            key: signaturePadKey,
                            backgroundColor: Colors.white,
                            strokeColor: Colors.black,
                            minimumStrokeWidth: 1.0,
                            maximumStrokeWidth: 4.0,
                          ),
                        ),
                      ),
                      const Divider(height: 30),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 10.0),
                              child: CustomButton(
                                height: 45,
                                fontSize: 14,
                                text: "Clear",
                                weight: FontWeight.w600,
                                textColor: AppColors.whiteColor,
                                color: AppColors.varientBlueColor,
                                onPressed: handleClear,
                              ),
                            ),
                          ),

                          // Continue / Get Started Button
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 10.0),
                              child: CustomButton(
                                height: 45,
                                fontSize: 14,
                                // Change button text based on the current page
                                text: "Sign Again",
                                weight: FontWeight.w600,
                                textColor: AppColors.whiteColor,
                                color: AppColors.blackColor,
                                onPressed: handleSignAgain,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),

                const SizedBox(height: 16),
                Row(
                  children: [
                    Checkbox(
                      value: isChecked,
                      onChanged: (bool? value) {
                        setState(() {
                          isChecked = value ?? false;
                        });
                      },
                      activeColor: AppColors.primaryColor,
                      // Light blue
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      side: const BorderSide(color: Colors.grey),
                    ),
                    const Expanded(
                      child: CustomText(
                        text:
                        'I confirm that all services have been completed to the customer\'s satisfaction.',
                        size: 12,
                        weight: FontWeight.w400,
                        color: AppColors.linghtTextGreyColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10.0),
                        child: CustomButton(
                          height: 45,
                          fontSize: 14,
                          text: "Cancel",
                          weight: FontWeight.w600,
                          textColor: AppColors.blackColor,
                          color: AppColors.unSelectedButton,
                          onPressed: () {},
                        ),
                      ),
                    ),

                    // Continue / Get Started Button
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10.0),
                        child: CustomButton(
                          height: 45,
                          fontSize: 14,
                          // Change button text based on the current page
                          text: "Generate Invoice",
                          weight: FontWeight.w600,
                          textColor: AppColors.whiteColor,
                          color: AppColors.blackColor,
                          onPressed: handleGenerateInvoice,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}