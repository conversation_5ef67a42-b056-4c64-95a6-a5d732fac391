import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:towchain_customer_app/presentation/screens/findService/subViews/drop_off_bottom_sheet.dart';
import 'package:towchain_customer_app/presentation/screens/findService/subViews/pick_up_bottom_sheet.dart';

import '../../../core/assets_manager.dart';
import '../../../core/theme/colors.dart';
import '../../../core/utils/validator.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_checkbox_drop_down.dart' hide AppColors;
import '../../../core/widgets/custom_drop_down.dart';
import '../../../core/widgets/custom_input_field.dart';
import '../../../core/widgets/custom_text.dart';

// Main Screen UI
class FindServiceDetailsScreen extends StatefulWidget {
  const FindServiceDetailsScreen({super.key});

  @override
  State<FindServiceDetailsScreen> createState() => _FindServiceScreenState();
}

class _FindServiceScreenState extends State<FindServiceDetailsScreen> {
  String _selectedOption = 'Roadside';
  final pickupController = TextEditingController();
  final dropOffController = TextEditingController();
  String? province;

  final List<String> _services = [
    'Truck mobile repair',
    'Trailer mobile repair',
    'Windshield repair',
    'Tire repair',
    'Fuel service',
  ];
  List<String> _selectedServices = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'Find Service',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              icon: SvgPicture.asset(
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              onPressed: () {
                context.pop(); // Uncomment for actual navigation
              },
            ),
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: CustomText(
              align: TextAlign.end,
              text: '4/4',
              size: 14,
              weight: FontWeight.w600,
              color: AppColors.whiteColor,
            ),
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Blue header container
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 16.0,
            ),
            decoration: const BoxDecoration(color: Color(0xFF00CBFF)),
            child: const Text(
              'You have selected Mobile Services For\nHeavy Vehicle of Tow.',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: 24.0,
                vertical: 10.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Roadside and Pick Up & Drop Off Radios
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      _buildRadioOption('Roadside'),
                      const SizedBox(width: 20),
                      _buildRadioOption('Pick Up & Drop Off'),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      CustomText(
                        text: "Pick Up",
                        color: AppColors.blackColor,
                        size: 14,
                        weight: FontWeight.w500,
                      ),
                      CustomText(
                        text: "*",
                        color: AppColors.primaryRedColor,
                        size: 14,
                        weight: FontWeight.w500,
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  Column(
                    children: [
                      TextField(
                        decoration: InputDecoration(
                          hintText:"Type your pick up location here" ,
                          hintStyle: TextStyle(
                            color: AppColors.hintColor,
                            fontSize: 14,
                            fontFamily: "Inter-Regular",
                            fontWeight: FontWeight.w400,
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 12,
                            // Adjust padding for multi-line

                            // Adjust padding for multi-line
                            horizontal: 12,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ),
                            borderSide: const BorderSide(
                              color: AppColors.primaryBorderColor,
                              width: 1,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ),
                            borderSide: BorderSide(
                              color: AppColors.lightestGreyColor,
                              width: 1,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ),
                            borderSide: const BorderSide(
                              color: AppColors.primaryBorderColor,
                              width: 1,
                            ),
                          ),
                          isDense: true,
                        ),
                        readOnly: true,
                        // This makes the text field non-editable but tappable
                        onTap: () {
                          // This is the key part: showing the bottom sheet
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            // This makes it full screen
                            builder: (BuildContext context) {
                              return const PickUpBottomSheet();
                            },
                          );
                        },
                      ),
                    ],
                  ),

                  SizedBox(height: 10),
                  // Drop Off Text Field
                  Row(
                    children: [
                      CustomText(
                        text: "Drop Off",
                        color: AppColors.blackColor,
                        size: 14,
                        weight: FontWeight.w500,
                      ),
                      CustomText(
                        text: "*",
                        color: AppColors.primaryRedColor,
                        size: 14,
                        weight: FontWeight.w500,
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  Column(
                    children: [
                      TextField(
                        decoration: InputDecoration(
                          hintText:"Type your drop off location here" ,
                          hintStyle: TextStyle(
                            color: AppColors.hintColor,
                            fontSize: 14,
                            fontFamily: "Inter-Regular",
                            fontWeight: FontWeight.w400,
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 12,
                            // Adjust padding for multi-line

                            // Adjust padding for multi-line
                            horizontal: 12,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ),
                            borderSide: const BorderSide(
                              color: AppColors.primaryBorderColor,
                              width: 1,
                            ),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ),
                            borderSide: BorderSide(
                              color: AppColors.lightestGreyColor,
                              width: 1,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(
                              8,
                            ),
                            borderSide: const BorderSide(
                              color: AppColors.primaryBorderColor,
                              width: 1,
                            ),
                          ),
                          isDense: true,
                        ),
                        readOnly: true,
                        // This makes the text field non-editable but tappable
                        onTap: () {
                          // This is the key part: showing the bottom sheet
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            // This makes it full screen
                            builder: (BuildContext context) {
                              return const DropOffBottomSheet();
                            },
                          );
                        },
                      ),
                    ],
                  ),

                  // CustomInputField(
                  //   hintText: 'Type your drop off location here',
                  //   fontSize: 14,
                  //   controller: dropOffController,
                  //   keyboardType: TextInputType.text,
                  //   validator: Validator.validatePickup,
                  //   readOnly: true,
                  //   // This makes the text field non-editable but tappable
                  //   onTap: () {
                  //     // This is the key part: showing the bottom sheet
                  //     showModalBottomSheet(
                  //       context: context,
                  //       isScrollControlled: true, // This makes it full screen
                  //       builder: (BuildContext context) {
                  //         return const DropOffBottomSheet();
                  //       },
                  //     );
                  //   },
                  // ),
                  SizedBox(height: 10),
                  // Selected Tags
                  Wrap(
                    spacing: 8.0,
                    runSpacing: 4.0,
                    children: const [
                      _Chip(label: 'Fuel Services'),
                      _Chip(label: 'Engine Repair'),
                    ],
                  ),
                  const SizedBox(height: 10),
                  CustomCheckBoxDropDownMobile<String>(
                    value: null,
                    options: _services,
                    getDisplayString: (String fruit) => fruit,
                    onChanged: (String? newValue) {
                      // This is not used in multi-select, but is kept for compatibility.
                    },
                    selectedValues: _selectedServices,
                    onSelectionChanged: (List<String> newSelection) {
                      setState(() {
                        _selectedServices = newSelection;
                      });
                    },
                  ),
                  // CustomCheckBoxDropDownMobile<String>(
                  //   value: province,
                  //   options: ['Truck mobile repair', 'Trailer mobile repair', 'Windshield repair', 'Tire repair', 'Fuel service'],
                  //   onChanged: (val) {
                  //     setState(() {
                  //       province = val;
                  //     });
                  //   },
                  //   getDisplayString: (province) => province, selectedValues: [], onSelectionChanged: (List<String> value) {  },
                  // ),
                  // Select Service Dropdown
                  const SizedBox(height: 10),
                  CustomText(
                    text: "Description",
                    color: AppColors.blackColor,
                    size: 14,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(height: 10),

                  CustomInputField(
                    hintText:
                        'Briefly describe the issue or towing need (e.g., flat tire)',
                    fontSize: 14,
                    controller: dropOffController,
                    keyboardType: TextInputType.text,
                    maxLines: 5,
                    validator: Validator.validatePickup,
                  ),
                  SizedBox(height: 10),
                  // Audio Message
                  _buildAudioMessage(),
                  const SizedBox(height: 24),

                  // Add Photos
                  _buildAddPhotos(),
                ],
              ),
            ),
          ),
          // Buttons
          Padding(
            padding: const EdgeInsets.all(24.0),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true, // This makes it full screen
                        builder: (BuildContext context) {
                          return const DropOffBottomSheet();
                        },
                      );
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.black,
                      side: const BorderSide(color: Colors.black),
                      minimumSize: const Size.fromHeight(55),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: CustomButton(
                    height: 55,
                    fontSize: 16,
                    text: 'Continue',
                    weight: FontWeight.w700,
                    textColor: AppColors.whiteColor,
                    color: AppColors.blackColor,
                    onPressed: () {
                      context.push('/setLocationOnMap');
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadioOption(String title) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Radio<String>(
          value: title,
          groupValue: _selectedOption,
          onChanged: (String? value) {
            setState(() {
              _selectedOption = value!;
            });
          },
          activeColor: AppColors.primaryColor,
        ),
        CustomText(
          text: title,
          size: 14,
          weight: FontWeight.w600,
          color: AppColors.blackColor,
        ),
      ],
    );
  }

  Widget _buildAudioMessage() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.lightestGreyColor),
        borderRadius: BorderRadius.circular(8.0),
        color: AppColors.whiteColor,
      ),
      child: Row(
        children: [
          const Text(
            'Send Audio Message',
            style: TextStyle(color: AppColors.lightestGreyColor),
          ),
          const Spacer(),
          SvgPicture.asset(AssetsManager.mick, width: 24, height: 24),
        ],
      ),
    );
  }

  Widget _buildAddPhotos() {
    return Container(
      width: 120,
      height: 140,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: AppColors.hintColor,
          style: BorderStyle.solid,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: CustomPaint(
          painter: DashedBorderPainter(),
          child: Container(
            // The inner Container now holds the content and its decoration.
            decoration: BoxDecoration(
              color: AppColors.selectionMealPortion,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(AssetsManager.add, width: 24, height: 24),
                // Icon(Icons.add_circle_outline, color: AppColors.hintColor, size: 40),
                const SizedBox(height: 8),
                const Text(
                  'Add Photos',
                  style: TextStyle(
                    color: AppColors.blackColor,
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _Chip extends StatelessWidget {
  final String label;

  const _Chip({required this.label});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.lightestGreyColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomText(
            text: label,
            color: AppColors.blackColor,
            size: 12,
            weight: FontWeight.w600,
          ),
          const SizedBox(width: 4),
          const Icon(Icons.close, size: 16),
        ],
      ),
    );
  }
}

class DashedBorderPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = AppColors
          .lightestGreyColor // Your desired border color
      ..strokeWidth =
          1 // Your desired border width
      ..style = PaintingStyle.stroke;

    var path = Path();
    // This creates a path for a rounded rectangle
    path.addRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(10),
      ),
    );

    // Create a dashed path
    var dashedPath = Path();
    var dashWidth = 8.0; // Length of the dash
    var dashSpace = 4.0; // Space between dashes
    var currentLength = 0.0;
    var totalLength = path.computeMetrics().first.length;

    for (var metric in path.computeMetrics()) {
      while (currentLength < totalLength) {
        dashedPath.addPath(
          metric.extractPath(currentLength, currentLength + dashWidth),
          Offset.zero,
        );
        currentLength += dashWidth + dashSpace;
      }
    }

    canvas.drawPath(dashedPath, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
