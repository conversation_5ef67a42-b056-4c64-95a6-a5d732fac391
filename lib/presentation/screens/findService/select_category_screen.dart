import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:towchain_customer_app/core/theme/colors.dart';

import '../../../core/assets_manager.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class SelectCategoryScreen extends StatefulWidget {
  const SelectCategoryScreen({super.key});

  @override
  State<SelectCategoryScreen> createState() => _SelectCategoryScreenState();
}

class _SelectCategoryScreenState extends State<SelectCategoryScreen> {
  int _selectedIndex = -1;

  // A list of service data, including a label and an icon.
  final List<Map<String, dynamic>> _services = [
    {'label': 'Car Detailing', 'icon': AssetsManager.car},
    {'label': 'Tow', 'icon': AssetsManager.towing},
    {'label': 'Repair', 'icon': AssetsManager.repaire},
    {'label': 'Battery', 'icon': AssetsManager.battery},
    {'label': 'Fuel', 'icon': AssetsManager.fuel},
    {'label': 'Tire', 'icon': AssetsManager.tire},
  ];

  String? _selectedVehicle;
  final List<Map<String, dynamic>> vehicles = [
    {'type': 'Mobile Services', 'icon': AssetsManager.mobileservice},
    {'type': 'Repair Garage Services', 'icon': AssetsManager.repairgaradge},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Find Service',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              alignment: Alignment.topLeft,
              icon: SvgPicture.asset(
                alignment: Alignment.center,
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              // Open the drawer when the menu icon is pressed
              onPressed: () {
                context.pop();
              },
            ),
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: CustomText(
              align: TextAlign.end,
              text: '2/4',
              size: 14,
              weight: FontWeight.w600,
              color: AppColors.whiteColor,
            ),
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Blue header container
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 16.0,
            ),
            decoration: const BoxDecoration(color: Color(0xFF00CBFF)),
            child: const Text(
              'You have Selected Mobile Services \nFor Heavy Vehicle',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 24,
              ),
              child: Column(
                children: [
                  CustomText(
                    align: TextAlign.start,
                    text:
                    'Please Select a Service Category',
                    size: 14,
                    weight: FontWeight.w500,
                    color: AppColors.primaryGreyColor,
                  ),
                  const SizedBox(height: 26),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate:
                    const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 10,
                      mainAxisSpacing: 10,
                      childAspectRatio: 0.7,
                    ),
                    itemCount: _services.length,
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedIndex = index;
                          });
                        },
                        child: Column(
                          children: [
                            Container(
                              height: 100, // Adjust height as needed
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: _selectedIndex == index
                                    ? const Color(0xFF41D5FB) // Selected color
                                    : const Color(0xFFE0E0E0), // Default color
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(
                                    _services[index]['icon'],
                                    height: 60,
                                    width: 60,
                                    color: Colors.black, // No color change
                                  ),
                                  const SizedBox(height: 10),
                                ],
                              ),
                            ),
                            const SizedBox(height: 8), // Space between container and text
                            Text(
                              _services[index]['label'],
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.black, // No color change
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 10),
            width: double.infinity,
            child: Row(
              children: [
                // Skip Button - Visible only on the first two pages
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: CustomButton(
                        height: 55,
                        fontSize: 16,
                        text: "Cancel",
                        weight: FontWeight.w700,
                        textColor: AppColors.blackColor,
                        color: AppColors.unSelectedButton,
                        onPressed: () {
                          // context.push('/signup');

                        },
                      ),
                    ),
                  ),

                // Continue / Get Started Button
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: CustomButton(
                      height: 55,
                      fontSize: 16,
                      // Change button text based on the current page
                      text:  "Continue",
                      weight: FontWeight.w700,
                      textColor: AppColors.whiteColor,
                      color: AppColors.blackColor,
                        onPressed: () {
                          if (_selectedIndex != -1) {
                            context.push('/findServiceDetailsScreen');
                          } else {
                            // Optionally, show a message to the user to select a service
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Please select a service type.'),
                              ),
                            );
                          }
                        }
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 26),

        ],
      ),
    );
  }
}