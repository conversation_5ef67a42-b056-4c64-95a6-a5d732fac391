import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:towchain_customer_app/core/theme/colors.dart';

import '../../../core/assets_manager.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class SelectServiceTypeScreen extends StatefulWidget {
  const SelectServiceTypeScreen({super.key});

  @override
  State<SelectServiceTypeScreen> createState() => _SelectionScreenState();
}

class _SelectionScreenState extends State<SelectServiceTypeScreen> {
  String? _selectedVehicle;
  final List<Map<String, dynamic>> vehicles = [
    {
      'type': 'Mobile Services',
      'icon': AssetsManager.mobileservice,
    },
    {
      'type': 'Repair Garage Services',
      'icon': AssetsManager.repairgaradge,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Find Service',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              alignment: Alignment.topLeft,
              icon: SvgPicture.asset(
                alignment: Alignment.center,
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              // Open the drawer when the menu icon is pressed
              onPressed: () {
                context.pop();
              },
            ),
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: CustomText(
              align: TextAlign.end,
              text:  '2/4',
              size: 14,
              weight: FontWeight.w600,
              color: AppColors.whiteColor,
            ),
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header Section
            Container(
              color: AppColors.primaryColor,
              width: double.infinity,
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: CustomText(
                  text:  'You have Selected Heavy Vehicle  ',
                  size: 18,
                  weight: FontWeight.w700,
                  color: AppColors.whiteColor,
                ),
              ),
            ),
            // Main Content Area
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [

                    CustomText(
                      text:  'Please Select a Service Type for Your Heavy Vehicle',
                      size: 14,
                      weight: FontWeight.w600,
                      color: AppColors.primaryGreyColor,
                    ),
                    const SizedBox(height: 26),
                    // Vehicle Selection Cards
                    ...vehicles.map((vehicle) {
                      final isSelected = _selectedVehicle == vehicle['type'];
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedVehicle = vehicle['type'];
                          });
                        },
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOut,
                          margin: const EdgeInsets.only(bottom: 16),
                          padding: const EdgeInsets.all(24.0),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? const Color(0xFFEFF6FF)
                                : Colors.white,
                            borderRadius: BorderRadius.circular(26),
                            border: Border.all(
                              color: isSelected
                                  ? const Color(0xFF00CBFF)
                                  : const Color(0xFFD1D5DB),
                              width: isSelected ? 1.0 : 1.0,
                            ),
                            boxShadow: isSelected
                                ? const [
                              BoxShadow(
                                color: Color(0x1A000000),
                                blurRadius: 8,
                                offset: Offset(0, 4),
                              ),
                            ]
                                : [],
                          ),
                          child: SizedBox(
                            width: double.infinity,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                Column(
                                  children: [
                                    CustomText(
                                      text:  vehicle['type'],
                                      size: 18,
                                      weight: FontWeight.w700,
                                      color: AppColors.blackColor,
                                    ),

                                    const SizedBox(height: 15),
                                    Image.asset( vehicle['icon'], width: 130,height: 130,),
                                  ],
                                ),
                                if (isSelected)
                                  Positioned(
                                    top: 0,
                                    right: 0,
                                    child: Transform.translate(
                                      offset: const Offset(10, -10),
                                      child: SvgPicture.asset(
                                        alignment: Alignment.center,
                                        AssetsManager.check,
                                        height: 34,
                                        width: 34,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ],
                ),
              ),
            ),

            // Continue Button
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: SizedBox(
                width: double.infinity,
                child:
                CustomButton(
                    height: 55,
                    fontSize: 16,
                    // Change button text based on the current page
                    text: "Continue",
                    weight: FontWeight.w700,
                    textColor: AppColors.whiteColor,
                    color: AppColors.blackColor,
                    onPressed:(){
                      context.push('/selectCategoryType');

                    }

                ),

                // ElevatedButton(
                //   onPressed: _selectedVehicle != null
                //       ? () {
                //           ScaffoldMessenger.of(context).showSnackBar(
                //             SnackBar(
                //               content: Text(
                //                 'You have selected: $_selectedVehicle.',
                //               ),
                //               duration: const Duration(seconds: 2),
                //             ),
                //           );
                //         }
                //       : null,
                //   style: ElevatedButton.styleFrom(
                //     backgroundColor: _selectedVehicle != null
                //         ? const Color(0xFF1F2937)
                //         : const Color(0xFFD1D5DB),
                //     foregroundColor: Colors.white,
                //     padding: const EdgeInsets.symmetric(vertical: 16),
                //     shape: RoundedRectangleBorder(
                //       borderRadius: BorderRadius.circular(24),
                //     ),
                //     elevation: 0,
                //   ),
                //   child: const Text(
                //     'Continue',
                //     style: TextStyle(
                //       fontSize: 16,
                //       fontWeight: FontWeight.bold,
                //     ),
                //   ),
                // ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

