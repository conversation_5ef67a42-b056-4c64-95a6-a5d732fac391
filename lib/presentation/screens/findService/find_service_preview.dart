import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import '../../../core/assets_manager.dart';
import '../../../core/theme/colors.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class FindServicePreviewScreen extends StatefulWidget {
  const FindServicePreviewScreen({super.key});

  @override
  State<FindServicePreviewScreen> createState() =>
      _FindServicePreviewScreenState();
}

class _FindServicePreviewScreenState extends State<FindServicePreviewScreen> {
  // A list of maps to hold question data and selected options

  @override
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'Find Service',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              icon: SvgPicture.asset(
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              onPressed: () {
                context.pop(); // Uncomment for actual navigation
              },
            ),
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: CustomText(
              align: TextAlign.end,
              text: 'Edit',
              size: 14,
              weight: FontWeight.w600,
              color: AppColors.primaryColor,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          // Ensure the main Column aligns its children to the start
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                horizontal: 20.0,
                vertical: 16.0,
              ),
              decoration: const BoxDecoration(color: Color(0xFF00CBFF)),
              child: const Text(
                'You have selected Mobile Services For\nHeavy Vehicle of Tow.',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
                // Left-align this Text widget as well
                textAlign: TextAlign.left,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 25.0),
              child: Column(
                // This Column needs to align its children to the start
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSection(
                    title: 'PICK UP',
                    content: 'Westheimer Rd.Santa Ana, Illinois 85486',
                  ),
                  _buildSection(
                    title: 'DROP OFF',
                    content: 'Thornridge Cir. at 1901 Shiloh, Hawaii 81063',
                  ),
                  _buildSection(title: 'VEHICLE TYPE', content: 'Heavy Vehicle'),
                  _buildSection(title: 'SERVICE TYPE', content: 'Mobile Service'),
                  _buildSection(title: 'CATEGORY', content: 'Tow'),
                  _buildSection(
                    title: 'SERVICES',
                    child: Wrap(
                      spacing: 8.0,
                      children: [
                        _buildServiceChip('Fuel Services'),
                        _buildServiceChip('Engine Repair'),
                        _buildServiceChip('Engine Repair'),
                      ],
                    ),
                  ),
                  _buildSection(
                    title: 'DESCRIPTION',
                    content:
                    'Stuck in basement parking, front tire is flat. Car won\'t\nmove. Need towing to nearby service center.',
                  ),
                  const SizedBox(height: 26),
                  Image.asset(AssetsManager.rec),
                  // Container(
                  //   padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  //   decoration: BoxDecoration(
                  //     color: Colors.grey[200],
                  //     borderRadius: BorderRadius.circular(20),
                  //   ),
                  //   child: Row(
                  //     children: [
                  //       const Icon(Icons.play_arrow),
                  //       Expanded(
                  //         child: Padding(
                  //           padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  //           child: Container(
                  //             height: 20,
                  //             decoration: BoxDecoration(
                  //               borderRadius: BorderRadius.circular(10),
                  //               color: Colors.grey[300],
                  //             ),
                  //             child: const Row(
                  //               children: [
                  //                 SizedBox(width: 4),
                  //                 // This is a simplified representation of the sound wave.
                  //                 // In a real app, you would use a custom painter or a library.
                  //                 Expanded(
                  //                   child: LinearProgressIndicator(
                  //                     value: 0.5,
                  //                     backgroundColor: Colors.transparent,
                  //                     valueColor: AlwaysStoppedAnimation<Color>(
                  //                       Colors.blue,
                  //                     ),
                  //                   ),
                  //                 ),
                  //               ],
                  //             ),
                  //           ),
                  //         ),
                  //       ),
                  //       const Text('0:20'),
                  //       const SizedBox(width: 8),
                  //       const Icon(Icons.delete),
                  //     ],
                  //   ),
                  // ),
                  const SizedBox(height: 26),
                  // This ListView.builder is already building Columns that are
                  // left-aligned, so no change is needed here.
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: 5,
                    itemBuilder: (context, index) {
                      return Column(
                        // This Column's crossAxisAlignment is already start
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText(
                            align: TextAlign.left, // Already left-aligned
                            text: 'Is the vehicle driveable or completely immobilized?',
                            size: 12,
                            weight: FontWeight.w600,
                            color: AppColors.linghtTextGreyColor,
                          ),
                          SizedBox(height: 10),
                          CustomText(
                            align: TextAlign.left, // Already left-aligned
                            text: 'Driveable but not safe',
                            size: 14,
                            weight: FontWeight.w500,
                            color: AppColors.primaryGreyColor,
                          ),
                          SizedBox(height: 16), // Add space between questions
                        ],
                      );
                    },
                  ),
                  const SizedBox(height: 24),
                  // The Positioned widget is not needed here as this is not a Stack.
                  // A simple Align or just the widget itself will suffice.
                  // To left-align the button, we can wrap it in an Align widget.
                  Align(
                    alignment: Alignment.topLeft,
                    child: CustomButton(
                      height: 55,
                      fontSize: 16,
                      text: "Broadcast",
                      weight: FontWeight.w700,
                      textColor: AppColors.whiteColor,
                      color: AppColors.blackColor,
                      onPressed: () {
                        context.push('/home');
                      },
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    String? content,
    Widget? child,
  }) {
    return Column(
      // This Column needs to align its children to the start
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        CustomText(
          align: TextAlign.left, // Explicitly left-align this
          text: title,
          size: 12,
          weight: FontWeight.w500,
          color: AppColors.linghtTextGreyColor,
        ),
        const SizedBox(height: 5),
        if (content != null)
          CustomText(
            align: TextAlign.left, // Explicitly left-align this
            text: content,
            size: 14,
            weight: FontWeight.w500,
            color: AppColors.primaryGreyColor,
          ),
        if (child != null) child,
      ],
    );
  }

  Widget _buildServiceChip(String label) {
    return Chip(
      label: CustomText(
        align: TextAlign.left, // Explicitly left-align this
        text: label,
        size: 12,
        weight: FontWeight.w400,
        color: AppColors.primaryGreyColor,
      ),
      backgroundColor: AppColors.lightestGreyColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
    );
  }
}