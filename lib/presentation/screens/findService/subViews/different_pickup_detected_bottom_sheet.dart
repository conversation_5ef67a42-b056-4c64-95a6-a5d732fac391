import 'package:go_router/go_router.dart';

import '../../../../core/imports/core_imports.dart';
import '../../../../core/widgets/custom_button.dart';

class DifferentPickupDetectedBottomSheet extends StatelessWidget {
  // This is the data that will be passed to the bottom sheet.
  // We use the `final` keyword because the data is set once at creation.
  final String location;

  // The constructor takes the location as a required parameter.
  const DifferentPickupDetectedBottomSheet({super.key, required this.location});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // The handle at the top of the bottom sheet.
            Container(
              height: 4.0,
              width: 40.0,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.0),
              ),
              margin: const EdgeInsets.only(bottom: 16.0),
            ),

            Text(
              "Different Pickup Location Detected",
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 5.0),
            // "Set Pickup Location" text.
            const Text(
              'Your current location does not match the selected pickup point.',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16.0),
            // The main location text.
            Text(
              "Are you booking on behalf of someone else?",
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 34.0),
            // The "Confirm Pickup Location" button.
            Container(
              margin: EdgeInsets.symmetric(horizontal: 5),
              width: double.infinity,
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: CustomButton(
                          height: 55,
                          fontSize: 12,
                          // Change button text based on the current page
                          text:  "Yes, It’s for Someone Else",
                          weight: FontWeight.w700,
                          textColor: AppColors.whiteColor,
                          color: AppColors.blackColor,
                          onPressed: () {
                          }
                      ),
                    ),
                  ),
                  // Skip Button - Visible only on the first two pages
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: CustomButton(
                        height: 55,
                        fontSize: 12,
                        text: "No, I’ll Be at the Pickup",
                        weight: FontWeight.w700,
                        textColor: AppColors.blackColor,
                        color: AppColors.unSelectedButton,
                        onPressed: () {
                          // context.push('/signup');

                        },
                      ),
                    ),
                  ),

                  // Continue / Get Started Button

                ],
              ),
            ),

          ],
        ),
      ),
    );
  }
}
