import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/assets_manager.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/widgets/custom_text.dart';
import '../../../../core/widgets/location_input_filed.dart';

class DropOffBottomSheet extends StatelessWidget {
  const DropOffBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(

      initialChildSize: 1.0,
      // This makes it full screen initially
      minChildSize: 0.5,
      // Minimum size if user drags it down
      maxChildSize: 1.0,
      // Maximum size
      expand: true,
      builder: (BuildContext context, ScrollController scrollController) {
        return Scaffold(
          backgroundColor: Colors.white,

          appBar: AppBar(
            backgroundColor: Colors.black,
            toolbarHeight: 80.0, // Set the height of the AppBar
            leading: Padding(
              padding: const EdgeInsets.only(top: 15.0),
              child: IconButton(
                icon: SvgPicture.asset(
                  AssetsManager.backarrow,
                  height: 34,
                  width: 34,
                ),
                onPressed: () {
                  context.pop(); // Uncomment for actual navigation
                },
              ),
            ),
            title: Padding(
              padding: const EdgeInsets.only(top: 15.0),
              child: CustomText(
                text: 'Drop Off',
                fontFamily: 'Urbanist-SemiBold',
                size: 18,
                weight: FontWeight.w600,
                color: AppColors.whiteColor,
              ),
            ),
          ),
          body: SingleChildScrollView(
            controller: scrollController,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      CustomText(
                        text: "Drop Off",
                        color: AppColors.blackColor,
                        size: 14,
                        weight: FontWeight.w500,
                      ),
                      CustomText(
                        text: "*",
                        color: AppColors.primaryRedColor,
                        size: 14,
                        weight: FontWeight.w500,
                      ),
                    ],
                  ),
                  const SizedBox(height: 10.0),

                  LocationInputField(
                    hintText: 'Type your Drop Off location here',
                    readOnly: true,
                    onTap: () {},
                  ),

                  const SizedBox(height: 16.0),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        text: 'Recent places',
                        color: AppColors.blackColor,
                        size: 14,
                        weight: FontWeight.w600,
                      ),
                      TextButton(
                        onPressed: () {},
                        child: CustomText(
                          text: 'Clear All',
                          color: AppColors.linghtTextGreyColor,
                          size: 14,
                          weight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  const Divider(color: Colors.grey),
                  ListTile(
                    leading: SvgPicture.asset(
                      AssetsManager.pin,
                      height: 24,
                      width: 24,
                    ),
                    title: const Text(
                      'Set Location On Map',
                      style: TextStyle(
                        color: AppColors.primaryGreyColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Urbanist',
                      ),
                    ),
                    onTap: () {
                      // Handle map selection
                    },
                  ),
                  const Divider(),
                  ListTile(
                    leading: SvgPicture.asset(
                      AssetsManager.homelocation,
                      height: 24,
                      width: 24,
                    ),
                    title: const Text(
                      'Choose Garage',
                      style: TextStyle(
                        color: AppColors.primaryGreyColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        fontFamily: 'Urbanist',
                      ),
                    ),
                    onTap: () {
                      // Handle map selection
                    },
                  ),
                  const Divider(),
                  // List of recent places
                  _buildRecentPlaceItem(
                    'Office',
                    '2972 Westheimer Rd. Santa Ana, Illinois 85486',
                    '2.7km',
                  ),
                  _buildRecentPlaceItem(
                    'Office',
                    '2972 Westheimer Rd. Santa Ana, Illinois 85486',
                    '2.7km',
                  ),
                  _buildRecentPlaceItem(
                    'Office',
                    '2972 Westheimer Rd. Santa Ana, Illinois 85486',
                    '2.7km',
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentPlaceItem(String title, String subtitle, String distance) {
    return Column(
      children: [
        ListTile(
          leading: const Icon(Icons.access_time),
          title: Text(
            title,
            style: TextStyle(
              color: AppColors.primaryGreyColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              fontFamily: 'Urbanist',
            ),
          ),
          subtitle: Text(
            subtitle,
            style: TextStyle(
              color: AppColors.hintColor,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              fontFamily: 'Urbanist',
            ),
          ),
          trailing: Text(
            distance,
            style: TextStyle(
              color: AppColors.primaryGreyColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              fontFamily: 'Urbanist',
            ),
          ),

          onTap: () {
            // Handle location selection
          },
        ),
        const Divider(),
      ],
    );
  }
}
