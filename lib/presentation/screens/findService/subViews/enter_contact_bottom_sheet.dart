import 'package:go_router/go_router.dart';

import '../../../../core/imports/core_imports.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../auth/widgets/phone_form_field.dart';

class EnterContactBottomSheet extends StatelessWidget {

  EnterContactBottomSheet({super.key});
  final _formKey = GlobalKey<FormState>();

  final passwordController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // The handle at the top of the bottom sheet.
            Container(
              height: 4.0,
              width: 40.0,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.0),
              ),
              margin: const EdgeInsets.only(bottom: 16.0),
            ),

            Text(
              "Please enter the contact number of the person at the pickup location so our team can coordinate with them directly.",
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 20.0),
            // "Set Pickup Location" text.
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: Form(
                key: _formKey,
                child: PhoneFormField(), // Use the widget here
                    // Checkbox and term


              ),
            ),

            const SizedBox(height: 34.0),
            // The "Confirm Pickup Location" button.
            Container(
              width: double.infinity,
              child: Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: CustomButton(
                        height: 55,
                        fontSize: 14,
                        text: "Skip",
                        weight: FontWeight.w700,
                        textColor: AppColors.blackColor,
                        color: AppColors.unSelectedButton,
                        onPressed: () {
                          // context.push('/signup');

                        },
                      ),
                    ),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: CustomButton(
                          height: 55,
                          fontSize: 14,
                          // Change button text based on the current page
                          text:  "Save",
                          weight: FontWeight.w700,
                          textColor: AppColors.whiteColor,
                          color: AppColors.blackColor,
                          onPressed: () {
                          }
                      ),
                    ),
                  ),
                  // Skip Button - Visible only on the first two pages


                  // Continue / Get Started Button

                ],
              ),
            ),

          ],
        ),
      ),
    );
  }
}
