import 'package:go_router/go_router.dart';

import '../../../core/imports/core_imports.dart';

class PickupLocationBottomSheet extends StatelessWidget {
  // This is the data that will be passed to the bottom sheet.
  // We use the `final` keyword because the data is set once at creation.
  final String location;

  // The constructor takes the location as a required parameter.
  const PickupLocationBottomSheet({
    super.key,
    required this.location,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // The handle at the top of the bottom sheet.
            Container(
              height: 4.0,
              width: 40.0,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.0),
              ),
              margin: const EdgeInsets.only(bottom: 16.0),
            ),
            // "Set Pickup Location" text.
            const Text(
              'Set Pickup Location',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16.0),
            // The main location text.
            Text(
              location,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600
              ),
            ),
            const SizedBox(height: 34.0),
            // The "Confirm Pickup Location" button.
            SizedBox(
              width: double.infinity, // Makes the button full width.
              child: ElevatedButton(
                // Use a dark color for the button to match the image.
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.black87,
                  padding: const EdgeInsets.symmetric(vertical: 16.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                ),
                onPressed: () {
                  // This closes the bottom sheet and can optionally return a value.
                  context.push('/addServiceFaqScreen');
                },
                child: const Text(
                  'Confirm Pickup Location',
                  style: TextStyle(color: Colors.white, fontSize: 18.0),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}