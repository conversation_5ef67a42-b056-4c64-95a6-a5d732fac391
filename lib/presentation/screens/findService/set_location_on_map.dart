import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:towchain_customer_app/core/assets_manager.dart';
import 'package:towchain_customer_app/core/widgets/custom_button_with_icon.dart';
import 'package:towchain_customer_app/core/widgets/custom_text.dart';
import 'package:towchain_customer_app/presentation/screens/findService/pickup_location_bottom_sheet.dart';

import '../../../core/imports/core_imports.dart';
import '../../../core/widgets/custom_app_bar.dart';

// Import the Drawer widget
import 'package:flutter/material.dart';

import '../../../core/widgets/custom_button.dart';

class SetLocationOnMapScreen extends StatefulWidget {
  const SetLocationOnMapScreen({Key? key}) : super(key: key);

  @override
  State<SetLocationOnMapScreen> createState() => _SetLocationOnMapScreenState();
}

class _SetLocationOnMapScreenState extends State<SetLocationOnMapScreen> {
  late GoogleMapController mapController;
  final LatLng _center = const LatLng(
    45.521563,
    -122.677433,
  ); // Example coordinates

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;
  }

  Set<Marker> _markers = {};
  BitmapDescriptor? customMarkerIcon;

  @override
  void initState() {
    super.initState();
    _loadCustomMarkerIcon();
  }
  // Function to load the custom marker image
  Future<void> _loadCustomMarkerIcon() async {
    // You can resize your image here if needed, for better performance and display
    // customMarkerIcon = await BitmapDescriptor.fromAssetImage(
    //   const ImageConfiguration(size: Size(48, 48)), // Adjust size as needed
    //   'assets/setlocation.png', // Path to your image asset
    // );
    _addMarkers(); // Add markers once the icon is loaded
  }
  void _addMarkers() {
    // Only add markers if the custom icon has been loaded
    if (customMarkerIcon != null) {
      setState(() {
        _markers.add(
          Marker(
            markerId: const MarkerId('current_location'),
            position: _center,
            infoWindow: const InfoWindow(
              title: 'Sahibzada Ajit Singh Nagar',
              snippet: 'My Custom Location',
            ),
            icon: customMarkerIcon!, // Assign your custom icon here
          ),
        );
        // You can add more markers with different images or the same one
        _markers.add(
          Marker(
            markerId: const MarkerId('another_place'),
            position: const LatLng(30.7333, 76.7794), // Example: Chandigarh
            infoWindow: const InfoWindow(
              title: 'Nearby City',
              snippet: 'Another custom marker',
            ),
            icon: customMarkerIcon!, // Reusing the same custom icon
          ),
        );
      });
    }
  }
  @override
  Widget build(BuildContext context) {
    void _showPickupLocationDialog() {
      showModalBottomSheet<void>(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.0)),
        ),
        isScrollControlled: true,
        builder: (BuildContext context) {
          return const PickupLocationBottomSheet(
            location: 'Westheimer Rd.Santa Ana, Illinois 85486',
          );
        },
      );
    }

    // We'll use a Scaffold key to control the drawer.
    final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

    return Scaffold(
      key: _scaffoldKey, // Assign the key to the Scaffold
      appBar: CustomAppBar(
        title: 'Set Location On Map',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              alignment: Alignment.topLeft,
              icon: SvgPicture.asset(
                alignment: Alignment.center,
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              // Open the drawer when the menu icon is pressed
              onPressed: () {
                context.pop(); // Uncomment for actual navigation
              },
            ),
          ),
        ),
      ),
      // The body uses a Stack to layer widgets on top of each other.
      body: Stack(
        children: [
          GoogleMap(
            onMapCreated: _onMapCreated,
            initialCameraPosition: CameraPosition(
              target: _center,
              zoom: 11.0,
            ),
            markers: _markers, // Your set of markers with custom icons

          ),
          // A placeholder for the map. In a real app, this would be a map widget.
          // Image.asset(
          //   AssetsManager.mapbg, // Placeholder image asset
          //   fit: BoxFit.cover,
          //   height: double.infinity,
          //   width: double.infinity,
          // ),
          Positioned(
            // Use a combination of properties to center the widget horizontally and vertically.
            top: 0,
            bottom: 0,
            left: 0,
            right: 0,
            child: Align(
              // The Align widget ensures the child is centered within its parent `Positioned` widget.
              alignment: Alignment.center,
              child: Transform.translate(
                // We use Transform.translate to slightly adjust the icon's position
                // so the tip of the pin is exactly in the center, not the base.
                offset: const Offset(0.0, -35.0),
                child: Image.asset(
                  AssetsManager.setlocation, // Assumes this asset exists
                  height: 100, // Adjust height and width as needed
                  width: 100,
                ),
              ),
            ),
          ),
          // Positioned widget for the group of floating buttons on the right.
          Positioned(
            right: 15.0,
            top: MediaQuery.of(context).size.height * 0.25, // Adjust position
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30.0),
                  border: Border.all(
                    color: AppColors.lightGreyColor,
                    width: 1.0,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(3.0),
                  child: Column(
                    children: [
                      // Compass-like button
                      FloatingActionButton(
                        mini: true,
                        onPressed: () {},
                        backgroundColor: Colors.white,
                        shape: const CircleBorder(),
                        child: Image.asset(
                          height: 15,
                          width: 15,
                          AssetsManager.locate,
                        ),
                      ),
                      const SizedBox(height: 5),
                      // Map style button
                      FloatingActionButton(
                        mini: true,
                        onPressed: () {},
                        backgroundColor: Colors.white,
                        shape: const CircleBorder(),
                        child: Image.asset(
                          height: 25,
                          width: 25,
                          AssetsManager.mapmini,
                        ),
                      ),
                      const SizedBox(height: 5),
                      // Plus button for zoom in
                      FloatingActionButton(
                        mini: true,
                        onPressed: () {},
                        backgroundColor: Colors.white,
                        shape: const CircleBorder(),
                        child: Image.asset(
                          height: 25,
                          width: 25,
                          AssetsManager.plus,
                        ),
                      ),
                      const SizedBox(height: 5),
                      // Minus button for zoom out
                      FloatingActionButton(
                        mini: true,
                        onPressed: () {},
                        backgroundColor: Colors.white,
                        shape: const CircleBorder(),
                        child: Image.asset(
                          height: 25,
                          width: 25,
                          AssetsManager.minus,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            left: 20.0,
            right: 20.0,
            bottom: 20.0,
            child: CustomButton(
              height: 55,
              fontSize: 16,
              // Change button text based on the current page
              text: "Pick Up Location",
              weight: FontWeight.w700,
              textColor: AppColors.whiteColor,
              color: AppColors.blackColor,
              onPressed: () {
                //  if (_formKey.currentState!.validate()) {
                _showPickupLocationDialog(); //  }
              },
            ),
          ),
        ],
      ),
    );
  }
}
