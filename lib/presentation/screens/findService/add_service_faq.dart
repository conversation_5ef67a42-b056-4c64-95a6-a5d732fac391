import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import '../../../core/assets_manager.dart';
import '../../../core/theme/colors.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class AddServiceFaqScreen extends StatefulWidget {
  const AddServiceFaqScreen({super.key});

  @override
  State<AddServiceFaqScreen> createState() => _AddServiceFaqScreenState();
}

class _AddServiceFaqScreenState extends State<AddServiceFaqScreen> {
  // A list of maps to hold question data and selected options
  final List<Map<String, dynamic>> _questions = [
    {
      'question': 'Is the vehicle drivable or completely immobilized?',
      'options': [
        'Driveable but not safe',
        'Completely immobilized',
        'Unknown condition',
      ],
      'selectedOption': '',
    },
    {
      'question': 'Is the vehicle in a safe and accessible location?',
      'options': [
        'Yes',
        'No (e.g., blocked, in ditch, tight space, etc.)',
      ],
      'selectedOption': '',
    },
    {
      'question': 'What type of tow truck do you prefer (if known)?',
      'options': [
        'Flatbed Tow',
        'Wheel-Lift Tow',
        'Hook & Chain',
        'Not Sure',
      ],
      'selectedOption': '',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Find Service',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              icon: SvgPicture.asset(
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              onPressed: () {
                context.pop(); // Uncomment for actual navigation
              },
            ),
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: CustomText(
              align: TextAlign.end,
              text: '4/4',
              size: 14,
              weight: FontWeight.w600,
              color: AppColors.whiteColor,
            ),
          ),
        ],
      ),

      body: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top info box
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20.0,
                    vertical: 16.0,
                  ),
                  decoration: const BoxDecoration(color: Color(0xFF00CBFF)),
                  child: const Text(
                    'You have selected Mobile Services For\nHeavy Vehicle of Tow.',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Dynamically generate question sections
                ..._questions.map((questionData) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15.0),
                        child: CustomText(
                         text:  questionData['question'],
                          fontFamily: 'Urbanist',
                          size: 14,
                          weight: FontWeight.w500,
                          color: AppColors.primaryGreyColor,
                        ),
                      ),

                      ...questionData['options'].map<Widget>((option) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: CheckboxListTile(
                            checkColor: AppColors.whiteColor,
                            activeColor: AppColors.primaryColor ,
                            side: BorderSide(color: AppColors.primaryColor),

                            title:    CustomText(
                              text:  option,
                              fontFamily: 'Urbanist',
                              size: 14,
                              weight: FontWeight.w400,
                              color: AppColors.primaryGreyColor,
                            ),
                            value: questionData['selectedOption'] == option,
                            onChanged: (bool? newValue) {
                              setState(() {
                                if (newValue!) {
                                  questionData['selectedOption'] = option;
                                } else {
                                  questionData['selectedOption'] = '';
                                }
                              });
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        );
                      }).toList(),
                      const SizedBox(height: 24),
                    ],
                  );
                }).toList(),
                Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () {

                          },
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.black,
                            side: const BorderSide(color: Colors.black),
                            minimumSize: const Size.fromHeight(55),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('Cancel'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: CustomButton(
                          height: 55,
                          fontSize: 16,
                          text: 'Continue',
                          weight: FontWeight.w700,
                          textColor: AppColors.whiteColor,
                          color: AppColors.blackColor,
                          onPressed: () {
                            context.push('/findServicePreviewScreen');
                          },
                        ),
                      ),
                    ],
                  ),
                ),

              ],
            ),
          ),
        ],
      ),
    );
  }
}