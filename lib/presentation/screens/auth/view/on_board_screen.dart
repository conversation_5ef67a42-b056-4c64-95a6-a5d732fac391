import 'package:go_router/go_router.dart';
import 'package:towchain_customer_app/main.dart';

import '../../../../core/imports/core_imports.dart';
import '../../../../core/widgets/custom_button.dart';
import '../model/slider_model.dart';
import 'package:flutter/material.dart';

class OnBoardScreen extends StatefulWidget {
  @override
  _OnBoardScreenState createState() => _OnBoardScreenState();
}

class _OnBoardScreenState extends State<OnBoardScreen> {
  late List<SliderModel> slides;
  int currentIndex = 0;
  late PageController _controller;

  @override
  void initState() {
    super.initState();
    _controller = PageController(initialPage: 0);
    slides = getSlides();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: PageView.builder(
              scrollDirection: Axis.horizontal,
              controller: _controller,
              onPageChanged: (value) {
                setState(() {
                  currentIndex = value;
                });
              },
              itemCount: slides.length,
              itemBuilder: (context, index) {
                return OnBoardingPage(
                  image: slides[index].image,
                  title: slides[index].title,
                  description: slides[index].description,
                );
              },
            ),
          ),
          SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              slides.length,
                  (index) => buildDot(index, context),
            ),
          ),
          SizedBox(height: 40),

          Divider(height: 1, color: AppColors.divider),
          SizedBox(height: 25),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 10),
            width: double.infinity,
            child: Row(
              children: [
                // Skip Button - Visible only on the first two pages
                if (currentIndex != slides.length - 1)
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: CustomButton(
                        height: 55,
                        fontSize: 16,
                        text: "Skip",
                        weight: FontWeight.w700,
                        textColor: AppColors.blackColor,
                        color: AppColors.unSelectedButton,
                        onPressed: () {
                          context.push('/signup');

                        },
                      ),
                    ),
                  ),

                // Continue / Get Started Button
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0),
                    child: CustomButton(
                      height: 55,
                      fontSize: 16,
                      // Change button text based on the current page
                      text: currentIndex == slides.length - 1 ? "Get Started" : "Continue",
                      weight: FontWeight.w700,
                      textColor: AppColors.whiteColor,
                      color: AppColors.blackColor,
                      onPressed: () {
                        // Change button functionality based on the current page
                        if (currentIndex == slides.length - 1) {
                          // Navigate to the home screen on the last page
                          context.push('/signup');
                        } else {
                          // Go to the next page
                          _controller.nextPage(
                            duration: Duration(milliseconds: 100),
                            curve: Curves.bounceIn,
                          );
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 40),
        ],
      ),
      backgroundColor: Colors.white,
    );
  }

  // Dot Indicator
  Widget buildDot(int index, BuildContext context) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      height: 8,
      width: currentIndex == index ? 25 : 10,
      margin: EdgeInsets.symmetric(horizontal: 5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: currentIndex == index
            ? AppColors.primaryColor
            : AppColors.greyBorderColor,
      ),
    );
  }
}

class OnBoardingPage extends StatelessWidget {
  final String image;
  final String title;
  final String description;

  OnBoardingPage({
    required this.image,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 50),
          Image.asset(image),
          SizedBox(height: 55),
          Text(
            title,
            style: TextStyle(
              fontSize: 25,
              fontWeight: FontWeight.w700,
              color: Colors.black,
              fontFamily: 'Urbanist',
            ),
          ),
          SizedBox(height: 25),
          Padding(
            padding: const EdgeInsets.only(left: 15, right: 15),
            child: Text(
              description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontFamily: 'Urbanist',
              ),
            ),
          ),
        ],
      ),
    );
  }
}