import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/assets_manager.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/utils/validator.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_input_field.dart';
import '../../../../core/widgets/custom_text.dart';
import '../widgets/build_header.dart';
import '../widgets/phone_form_field.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenSate();
}

class _ForgotPasswordScreenSate extends State<ForgotPasswordScreen> {

  bool _agreeToTerms = false;

  // Key for form validation (a good practice to add)
  final _formKey = GlobalKey<FormState>();

  final passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
        CommonHeader(
            title: 'Forgot your password',
            subtitle: "Please enter your phone number below to receive\n your password reset instructions",
            clickableSubtitleText: "",
            backgroundImage: AssetsManager.authbg,
            onSubtitleClicked: () {
              // context.push('/signup');
            },
            // Replace with your image path
            onBackButtonPressed: () {
              if (Navigator.of(context).canPop()) {
                context.pop();
              } else {
                context.go('/login');
              }
            },
            // height: 200.0,
          ),
      // --- Form Section ---
            Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(32),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 24),
                      PhoneFormField(), // Use the widget here


                      // Checkbox and terms
                      const SizedBox(height: 32),
                      // Sign Up button
                      CustomButton(
                        height: 55,
                        fontSize: 16,
                        // Change button text based on the current page
                        text: "Send",
                        weight: FontWeight.w700,
                        textColor: AppColors.whiteColor,
                        color: AppColors.blackColor,
                        onPressed: () {
                          if (_formKey.currentState!.validate()) {
                            // TODO: Handle sign up logic
                          }
                        },

                      ),
                      const SizedBox(height: 20),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              context.go('/login');
                            },
                            child: Text(
                              'Back to Sign in',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                decoration: TextDecoration.underline,
                              ),
                            )

                          ),
                        ],
                      ),
                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}