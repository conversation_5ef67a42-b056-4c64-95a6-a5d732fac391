// A StatefulWidget for the screen with the gradient background.
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:towchain_customer_app/core/widgets/custom_text.dart';

import '../../../../core/assets_manager.dart';
import '../../../../core/imports/core_imports.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/gradient_background.dart';

class EnableLocationScreen extends ConsumerStatefulWidget {
  const EnableLocationScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<EnableLocationScreen> createState() =>
      _EnableLocationScreenState();
}

class _EnableLocationScreenState extends ConsumerState<EnableLocationScreen> {
  @override
  Widget build(BuildContext context) {
    // The Scaffold provides a framework for the app's visual structure.
    return Scaffold(
      // The body of the Scaffold is now our common GradientBackground widget.
      body: GradientBackground(
        // We pass the desired colors to the widget.
        colors: const [
          Color(0xFFFFFFFF), // A shade of blue
          Color(0xFFFFFFFF), // A shade of blue
          Color(0xFFFFFFFF), // A shade of blue
          Color(0xFFBCF1FF), // A shade of dark gray
        ],
        // The child is the content that appears on top of the gradient.
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(height: 50),
              Image.asset(
                AssetsManager.map,
                height: 400,
                alignment: Alignment.center,
              ),
              const CustomText(
                text: "Enable Location",
                size: 25,
                weight: FontWeight.w700,
                color: Colors.black,
              ),

              const SizedBox(height: 20),
              const Padding(
                padding:  EdgeInsets.only(left: 15, right: 15),
                child:   CustomText(
                  align: TextAlign.center,
                  text: "Choose Your location to start find \nthe request around you    ",
                  size: 16,
                  weight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 32),
              // Sign Up button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: CustomButton(
                  height: 55,
                  fontSize: 16,
                  // Change button text based on the current page
                  text: "Use My Location",
                  weight: FontWeight.w700,
                  textColor: AppColors.whiteColor,
                  color: AppColors.blackColor,
                  onPressed: () {
                  },

                ),
              ),
              const SizedBox(height: 20),

              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                      onTap: () {
                        context.push('/home');
                      },
                      child: Text(
                        'Skip for now',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                        ),
                      )

                  ),
                ],
              ),
              const SizedBox(height: 20),

            ],
          ),
        ),
      ),
    );
  }
}
