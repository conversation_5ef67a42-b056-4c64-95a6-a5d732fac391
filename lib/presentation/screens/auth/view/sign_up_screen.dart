import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/assets_manager.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/utils/validator.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_input_field.dart';
import '../../../../core/widgets/custom_text.dart';
import '../widgets/build_header.dart';
import '../widgets/phone_form_field.dart';

class SignupScreen extends StatefulWidget {
  const SignupScreen({super.key});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {

  bool _agreeToTerms = false;

  // Key for form validation (a good practice to add)
  final _formKey = GlobalKey<FormState>();

  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();

  final passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [

            // --- Header Section ---
            CommonHeader(
              title: 'Create Your Account',
              subtitle: "Already have an account?",
              clickableSubtitleText: "Login",
              backgroundImage: AssetsManager.authbg,
              onSubtitleClicked: () {
                context.go('/login');
              },
              // Replace with your image path
              onBackButtonPressed: () {
                if (Navigator.of(context).canPop()) {
                  context.pop();
                } else {
                  context.go('/login');
                }
              },
            ), // --- Form Section ---
            Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(  
                  topLeft: Radius.circular(32),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 24),
                      Row(
                        children: [
                          CustomText(
                            text: "First Name",
                            color: AppColors.blackColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                          CustomText(
                            text: "*",
                            color: AppColors.primaryRedColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      CustomInputField(
                        hintText: 'Type your first name here',
                        fontSize: 14,
                        controller: firstNameController,
                        keyboardType: TextInputType.name,
                        validator: Validator.validateFName,
                      ),
                      SizedBox(height: 10,),

                      Row(
                        children: [
                          CustomText(
                            text: "Last Name",
                            color: AppColors.blackColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                          CustomText(
                            text: "*",
                            color: AppColors.primaryRedColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      CustomInputField(
                        hintText: 'Type your last name here',
                        fontSize: 14,
                        controller: lastNameController,
                        keyboardType: TextInputType.name,
                        validator: Validator.validateLName,
                      ),
                      SizedBox(height: 10,),

                      Row(
                        children: [
                          CustomText(
                            text: "Email Id",
                            color: AppColors.blackColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                          CustomText(
                            text: "*",
                            color: AppColors.primaryRedColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      CustomInputField(
                        hintText: 'Enter email address',
                        fontSize: 14,
                        controller: emailController,
                        keyboardType: TextInputType.emailAddress,
                        validator: Validator.validateEmail,
                      ),
                      SizedBox(height: 10,),
                      Row(
                        children: [
                          CustomText(
                            text: "Password",
                            color: AppColors.blackColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                          CustomText(
                            text: "*",
                            color: AppColors.primaryRedColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      CustomInputField(
                        hintText: '***********',
                        isPassword: true,
                        fontSize: 14,
                        controller: passwordController,
                        keyboardType: TextInputType.text,
                        validator: Validator.validatePassword,

                      ),
                      SizedBox(height: 10,),
                      Row(
                        children: [
                          CustomText(
                            text: "Confirm Password",
                            color: AppColors.blackColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                          CustomText(
                            text: "*",
                            color: AppColors.primaryRedColor,
                            size: 14,
                            weight: FontWeight.w500,

                          ),
                        ],
                      ),
                      SizedBox(height: 10,),
                      CustomInputField(
                        hintText: '***********',
                        isPassword: true,
                        fontSize: 14,
                        controller: passwordController,
                        keyboardType: TextInputType.text,
                        validator: Validator.validatePassword,
                      ),
                      SizedBox(height: 10,),

                      PhoneFormField(), // Use the widget here
                      // Checkbox and terms
                      _buildTermsAndConditionsCheckbox(),
                      const SizedBox(height: 32),
                      // Sign Up button
                      CustomButton(
                        height: 55,
                        fontSize: 16,
                        // Change button text based on the current page
                        text: "Sign Up",
                        weight: FontWeight.w700,
                        textColor: AppColors.whiteColor,
                        color: AppColors.blackColor,
                        onPressed: () {
                        //  if (_formKey.currentState!.validate()) {
                            context.push('/verifyOtp');
                        //  }
                        },

                      ),

                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }





  Widget _buildTermsAndConditionsCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 24,
          height: 24,
          child: Checkbox(
            value: _agreeToTerms,
            onChanged: (bool? value) {
              setState(() {
                _agreeToTerms = value!;
              });
            },
            activeColor: AppColors.primaryColor,
            // Light blue
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4)),
            side: const BorderSide(color: Colors.grey),
          ),
        ),
        const SizedBox(width: 8),
        const Expanded(
          child: Text(
            'By clicking "Sign up" you agree to our terms & condition as well as our privacy policy.',
            style: TextStyle(fontSize: 12,
            fontWeight: FontWeight.w400,
            fontFamily: 'Urbaist'),
          ),
        ),
      ],
    );
  }

  InputDecoration _inputDecoration(String hintText,
      {Widget? suffixIcon, Widget? prefixIcon, BoxConstraints? prefixIconConstraints}) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: const TextStyle(color: Colors.grey),
      filled: true,
      fillColor: const Color(0xFFF5F5F5),
      // Lighter grey for fill
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.transparent),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: Colors.blue, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(
          vertical: 16.0, horizontal: 16.0),
      suffixIcon: suffixIcon,
      prefixIcon: prefixIcon,
      prefixIconConstraints: prefixIconConstraints,
    );
  }
}