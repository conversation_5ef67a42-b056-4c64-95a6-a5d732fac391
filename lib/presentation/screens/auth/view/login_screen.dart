import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/assets_manager.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/utils/validator.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/custom_input_field.dart';
import '../../../../core/widgets/custom_text.dart';
import '../widgets/build_header.dart';
import '../widgets/phone_form_field.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {

  bool _agreeToTerms = false;

  // Key for form validation (a good practice to add)
  final _formKey = GlobalKey<FormState>();

  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();

  final passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [

          // --- Header Section ---
          CommonHeader(
            title: 'Welcome Back',
            subtitle: "Don’t have an account?",
            clickableSubtitleText: "Sign Up",
            backgroundImage: AssetsManager.authbg,
            onSubtitleClicked: () {
              context.go('/signup');
            },
            // Replace with your image path
            onBackButtonPressed: () {
              if (Navigator.of(context).canPop()) {
                context.pop();
              } else {
                context.go('/onboard');
              }
            },
          ), // --- Form Section ---
          Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(32),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 24),
                    PhoneFormField(), // Use the widget here

                    Row(
                      children: [
                        CustomText(
                          text: "Password",
                          color: AppColors.blackColor,
                          size: 14,
                          weight: FontWeight.w500,

                        ),
                        CustomText(
                          text: "*",
                          color: AppColors.primaryRedColor,
                          size: 14,
                          weight: FontWeight.w500,

                        ),
                      ],
                    ),
                    SizedBox(height: 10,),

                    CustomInputField(
                      hintText: '***********',
                      isPassword: true,
                      fontSize: 14,
                      controller: passwordController,
                      keyboardType: TextInputType.text,
                      validator: Validator.validatePassword,

                    ),
                    SizedBox(height: 15,),
                    Row(
                      children: [
                        const Spacer(),
                        GestureDetector(
                          onTap: () {
                            context.go('/forgotPassword');
                          },
                          child: CustomText(
                            text: "Forgot Password?",
                            color: AppColors.blackColor,
                            size: 14,
                            weight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    // Checkbox and terms
                    const SizedBox(height: 32),
                    // Sign Up button
                    CustomButton(
                      height: 55,
                      fontSize: 16,
                      // Change button text based on the current page
                      text: "Log In",
                      weight: FontWeight.w700,
                      textColor: AppColors.whiteColor,
                      color: AppColors.blackColor,
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          // TODO: Handle sign up logic
                        }
                      },

                    ),

                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}