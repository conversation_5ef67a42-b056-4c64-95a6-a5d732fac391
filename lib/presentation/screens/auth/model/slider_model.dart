import 'package:towchain_customer_app/core/assets_manager.dart';

class SliderModel {
  final String image;
  final String title;
  final String description;

  // Constructor for variables
  SliderModel({
    required this.title,
    required this.description,
    required this.image,
  });
}

// List of onboarding slides
List<SliderModel> getSlides() {
  return [
    SliderModel(
      image: AssetsManager.on_board_one,
      title: "Broadcast Your Request",
      description: "Lorem ipsum si amet, cinsecteur adipicing elit, sed do elusmod tempor Lorem ipsum si amet..",
    ),
    SliderModel(
      image: AssetsManager.on_board_two,
      title: "Track the Service Progress",
      description: "Lorem ipsum si amet, cinsecteur adipicing elit, sed do elusmod tempor Lorem ipsum si amet..",
    ),
    SliderModel(
      image: AssetsManager.on_board_three,
      title: "Get Service Done",
      description: "Lorem ipsum si amet, cinsecteur adipicing elit, sed do elusmod tempor Lorem ipsum si amet..",
    ),
  ];
}