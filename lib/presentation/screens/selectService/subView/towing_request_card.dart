import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:towchain_customer_app/core/widgets/custom_text.dart';
import 'package:towchain_customer_app/presentation/screens/selectService/subView/confirm_request_card.dart';

import '../../../../core/assets_manager.dart';
import '../../../../core/imports/core_imports.dart';

class TowingRequestCard extends StatelessWidget {
  const TowingRequestCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4.0, // Provides the shadow effect
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0), // Rounded corners
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top section with driver info and price
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 40, // set height
                  width: 40,  // set width
                  child: const CircleAvatar(
                    backgroundImage: AssetImage('assets/images/user.png'),
                  ),
                ),
                const SizedBox(width: 12.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CustomText(
                            text: 'David Morel',
                            size: 18,
                            weight: FontWeight.w600,
                            color: AppColors.blackTextColor,
                          ),
                          const SizedBox(width: 8.0),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color:AppColors.yellowColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Row(
                              children: [
                                Text(
                                  '4.5',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Icon(Icons.star, color: Colors.black, size: 14),
                              ],
                            ),
                          ),
                        ],
                      ),
                      CustomText(
                        text: 'ID-12654',
                        size: 12,
                        weight: FontWeight.w500,
                        color: AppColors.lightGreyColor,
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    CustomText(
                      text: '\$60',
                      size: 25,
                      weight: FontWeight.w700,
                      color: AppColors.successColor,
                    ),
                    const Text(
                      'Hourly',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.lightGreyColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 10.0),

            // Middle section with travel details and service
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildIconText('15min', AssetsManager.clock),
                const SizedBox(width: 4.0),

                _buildIconText('2.5km away', AssetsManager.ic_navigate),
                const SizedBox(width: 4.0),

                _buildIconText('Emergency Towing', AssetsManager.emertow),
              ],
            ),
            const Divider(height: 24.0, thickness: 1.0),
            CustomText(
              text: '\$20 Minimum payment in advance',

              size: 14,
              weight: FontWeight.w600,
              color: AppColors.warningColor,
            ),
            // Minimum payment section

            const Divider(height: 24.0, thickness: 1.0),

            GestureDetector(
              onTap: () {
                context.push('/paymentDetailsScreen');

              },
              child: Text(
                '\$150 Visiting Fees',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.successColor,
                ),
              ),
            ),
            const SizedBox(height: 16.0),

            // Buttons section
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      _showConfirmRequestBottomSheet(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                    ),
                    child: const Text(
                      'Accept',
                      style: TextStyle(color: Colors.white,fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                const SizedBox(width: 12.0),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      //context.pop();
                      context.push('/completesPaymentDetailsScreen');

                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF333333),
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                    ),
                    child: const Text(
                      'Decline',
                      style: TextStyle(color: Colors.white,fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconText(String text, String imagePath) {
    return Row(
      children: [
        SvgPicture.asset(imagePath, height: 15, width: 15),
        const SizedBox(width: 4.0),
        CustomText(
          text: text,
          size: 12,
          weight: FontWeight.w600,
          color: AppColors.primaryGreyColor,
        ),
      ],
    );
  }

  void _showConfirmRequestBottomSheet(BuildContext context) {
    // First, close the current bottom sheet
    Navigator.of(context).pop();

    // Then show the confirm request card as a bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.4,
          minChildSize: 0.4,
          maxChildSize: 0.8,
          expand: false,
          builder: (BuildContext context, ScrollController scrollController) {
            return Container(
              // decoration: const BoxDecoration(
              //   color: Colors.white,
              //   borderRadius: BorderRadius.only(
              //     topLeft: Radius.circular(20),
              //     topRight: Radius.circular(20),
              //   ),
              // ),
              child: SingleChildScrollView(
                controller: scrollController,
                child: const Padding(
                  padding: EdgeInsets.all(16.0),
                  child: ConfirmRequestCard(),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
