import 'dart:async';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:towchain_customer_app/core/widgets/custom_text.dart';

import '../../../../core/assets_manager.dart';
import '../../../../core/imports/core_imports.dart';

class ConfirmRequestCard extends StatefulWidget {
  const ConfirmRequestCard({super.key});

  @override
  State<ConfirmRequestCard> createState() => _ConfirmRequestCardState();
}

class _ConfirmRequestCardState extends State<ConfirmRequestCard> {
  int _currentState = 0; // 0: Cancel Service, 1: Towing Now..., 2: Pay
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    // After 3 seconds, change to "Towing Now..."
    _timer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _currentState = 1;
        });

        // After another 3 seconds, change to "Pay"
        _timer = Timer(const Duration(seconds: 3), () {
          if (mounted) {
            setState(() {
              _currentState = 2;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4.0, // Provides the shadow effect
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0), // Rounded corners
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top section with driver info and price
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 40, // set height
                  width: 40,  // set width
                  child: const CircleAvatar(
                    backgroundImage: AssetImage('assets/images/user.png'),
                  ),
                ),
                const SizedBox(width: 12.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CustomText(
                            text: 'David Morel',
                            size: 18,
                            weight: FontWeight.w600,
                            color: AppColors.blackTextColor,
                          ),
                          const SizedBox(width: 8.0),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color:AppColors.yellowColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Row(
                              children: [
                                Text(
                                  '4.5',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Icon(Icons.star, color: Colors.black, size: 14),
                              ],
                            ),
                          ),
                        ],
                      ),
                      CustomText(
                        text: 'ID-12654',
                        size: 12,
                        weight: FontWeight.w500,
                        color: AppColors.lightGreyColor,
                      ),
                    ],
                  ),
                ),
                SvgPicture.asset(AssetsManager.phone, height: 40, width: 40),

              ],
            ),

            const SizedBox(height: 10.0),
            // Pick Up and Drop Off Locations
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: 'PICK UP',
                  size: 12,
                  weight: FontWeight.w600,
                  color: AppColors.lightGreyColor,
                ),

                CustomText(
                  text: 'Westheimer Rd.Santa Ana. Illinois 85486',
                  size: 14,
                  weight: FontWeight.w500,
                  color: AppColors.texGreyColor,
                ),
              ],
            ),
            SizedBox(height: 5),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: 'DROP OFF',
                  size: 12,
                  weight: FontWeight.w600,
                  color: AppColors.lightGreyColor,
                ),

                CustomText(
                  text: 'Thornridge Cir. at 1901 Shiloh, Hawaii 81063',
                  size: 14,
                  weight: FontWeight.w500,
                  color: AppColors.texGreyColor,
                ),
              ],
            ),
            SizedBox(height: 10),

            // Middle section with travel details and service
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildIconText('\$100', AssetsManager.price),
                const SizedBox(width: 4.0),

                _buildIconText('2.5km away', AssetsManager.ic_navigate),
                const SizedBox(width: 4.0),

                _buildIconText('Emergency Towing', AssetsManager.emertow),
              ],
            ),

            const Divider(height: 24.0, thickness: 1.0),

            _buildBottomContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomContent() {
    switch (_currentState) {
      case 0:
        // Cancel Service button
        return ElevatedButton(
          onPressed: () {
            // Close any open dialogs first
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            }
            context.push('/completesPaymentDetailsScreen');
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.blackColor,
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            minimumSize: const Size(double.infinity, 55),
          ),
          child: const Text(
            'Cancel Service',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
        );
      case 1:
        // Towing Now... text
        return Container(
          height: 55,
          alignment: Alignment.center,
          child: const Text(
            'Towing Now...',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey,
            ),
          ),
        );
      case 2:
        // Pay button
        return ElevatedButton(
          onPressed: () {
            // Close any open dialogs first
            if (Navigator.of(context).canPop()) {
              Navigator.of(context).pop();
            }
            context.push('/completesPaymentDetailsScreen');
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.blackColor,
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            minimumSize: const Size(double.infinity, 55),
          ),
          child: const Text(
            'Pay',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w700,
            ),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildIconText(String text, String imagePath) {
    return Row(
      children: [
        SvgPicture.asset(imagePath, height: 15, width: 15),
        const SizedBox(width: 4.0),
        CustomText(
          text: text,
          size: 12,
          weight: FontWeight.w600,
          color: AppColors.primaryGreyColor,
        ),
      ],
    );
  }
}
