import 'package:flutter/material.dart';

import 'package:towchain_customer_app/presentation/screens/selectService/subView/towing_request_card.dart';
import '../../../../core/theme/colors.dart';


class SelectServiceProviderSheet extends StatelessWidget {
  const SelectServiceProviderSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.9, // Start with a small size at the bottom
      minChildSize: 0.9,     // Minimum size
      maxChildSize: 0.9,     // Maximum size, allowing it to expand
      expand: true,
      builder: (BuildContext context, ScrollController scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.transparent, // Or your preferred background color
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min, // Essential: makes Column take minimum space
            children: [
              // You can add a drag handle here
              Expanded( // Allows the ListView to fill the remaining space
                child: ListView.builder(
                  controller: scrollController,
                  itemCount: 2, // The number of cards you want to display
                  itemBuilder: (context, index) {
                    return const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: TowingRequestCard(), // Use your card widget here
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}