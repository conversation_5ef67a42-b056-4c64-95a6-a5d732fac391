import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';

import '../../../core/assets_manager.dart';
import '../../../core/theme/colors.dart';
import '../../../core/widgets/custom_app_bar.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/custom_text.dart';

class PaymentDetailsScreen extends StatefulWidget {
  const PaymentDetailsScreen({super.key});

  @override
  State<PaymentDetailsScreen> createState() => _PaymentDetailsScreenState();
}

class _PaymentDetailsScreenState extends State<PaymentDetailsScreen> {
  bool _agreeToTerms = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'Payment',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              icon: SvgPicture.asset(
                AssetsManager.backarrow,
                height: 34,
                width: 34,
              ),
              onPressed: () {
                context.pop(); // Uncomment for actual navigation
              },
            ),
          ),
        ),
      ),

      body: SingleChildScrollView(
        child: Column(
          children: [
            // Advance Payment Section
            Container(
              margin: const EdgeInsets.symmetric(
                horizontal: 10.0,
                vertical: 25.0,
              ),
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomText(
                    text: 'Advance Payment',
                    size: 16,
                    weight: FontWeight.w700,
                    color: AppColors.successColor,
                  ),

                  const SizedBox(height: 5),
                  CustomText(
                    text: 'Minimum advance payment towing service fee',
                    size: 14,
                    weight: FontWeight.w500,
                    color: AppColors.linghtTextGreyColor,
                  ),

                  const SizedBox(height: 10),
                  const Center(
                    child: CustomText(
                      text: '\$10.00',
                      size: 24,
                      weight: FontWeight.w700,
                      color: AppColors.texGreyColor,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 10),
            Divider(thickness: 12, height: 10, color: AppColors.divider),

            // Details Section
            Container(
              padding: const EdgeInsets.all(20.0),

              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                    text: 'Details',
                    size: 18,
                    weight: FontWeight.w700,
                    color: AppColors.texGreyColor,
                  ),

                  const Divider(height: 30),

                  // Profile Section
                  Row(
                    children: [
                      const CircleAvatar(
                        backgroundImage: AssetImage('assets/images/user.png'),
                        // Replace with your image asset
                        radius: 30.0,
                      ),
                      const SizedBox(width: 10),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomText(
                              text: 'David Morel',
                              size: 18,
                              weight: FontWeight.w600,
                              color: AppColors.blackTextColor,
                            ),
                            CustomText(
                              text: 'ID-12654',
                              size: 14,
                              weight: FontWeight.w500,
                              color: AppColors.lightGreyColor,
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8.0,
                          vertical: 4.0,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.yellowColor,
                          borderRadius: BorderRadius.circular(5.0),
                        ),
                        child: const Row(
                          children: [
                            CustomText(
                              text: '4.5',
                              size: 14,
                              weight: FontWeight.w600,
                              color: AppColors.texGreyColor,
                            ),

                            Icon(Icons.star, size: 16.0, color: Colors.black),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const Divider(height: 30),

                  // Pick Up and Drop Off Locations
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        text: 'PICK UP',
                        size: 12,
                        weight: FontWeight.w600,
                        color: AppColors.lightGreyColor,
                      ),

                      SizedBox(height: 5),
                      CustomText(
                        text: 'Westheimer Rd.Santa Ana. Illinois 85486',
                        size: 14,
                        weight: FontWeight.w500,
                        color: AppColors.texGreyColor,
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                        text: 'DROP OFF',
                        size: 12,
                        weight: FontWeight.w600,
                        color: AppColors.lightGreyColor,
                      ),

                      SizedBox(height: 5),
                      CustomText(
                        text: 'Thornridge Cir. at 1901 Shiloh, Hawaii 81063',
                        size: 14,
                        weight: FontWeight.w500,
                        color: AppColors.texGreyColor,
                      ),
                    ],
                  ),
                  const Divider(height: 30),

                  // Price and Distance Details
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                            text: 'MINIMUM PAYMENT',
                            size: 12,
                            weight: FontWeight.w600,
                            color: AppColors.lightGreyColor,
                          ),
                          CustomText(
                            text: '\$10.00',
                            size: 14,
                            weight: FontWeight.w500,
                            color: AppColors.texGreyColor,
                          ),


                        ],
                      ),
                      SizedBox(height: 15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                            text: 'TOTAL PRICE',
                            size: 12,
                            weight: FontWeight.w600,
                            color: AppColors.lightGreyColor,
                          ),
                          CustomText(
                            text: '\$100.00',
                            size: 14,
                            weight: FontWeight.w500,
                            color: AppColors.texGreyColor,
                          ),


                        ],
                      ),
                      SizedBox(height: 15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                            text: 'DISTANCE',
                            size: 12,
                            weight: FontWeight.w600,
                            color: AppColors.lightGreyColor,
                          ),
                          CustomText(
                            text: '2.5km',
                            size: 14,
                            weight: FontWeight.w500,
                            color: AppColors.texGreyColor,
                          ),


                        ],
                      ),

                      SizedBox(height: 15),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                            text: 'SERVICE',
                            size: 12,
                            weight: FontWeight.w600,
                            color: AppColors.lightGreyColor,
                          ),
                          CustomText(
                            text: 'Emergency Towing',
                            size: 14,
                            weight: FontWeight.w500,
                            color: AppColors.texGreyColor,
                          ),


                        ],
                      ),

                    ],
                  ),
                  const Divider(height: 40),

                  _buildTermsAndConditionsCheckbox(),

                ],
              ),
            ),
            Container(
              padding: EdgeInsets.only(top: 40,left: 15,right: 15,bottom: 40),
              color: AppColors.divider,
              child: CustomButton(
                height: 55,
                fontSize: 16,
                // Change button text based on the current page
                text: "Pay & Book",
                weight: FontWeight.w700,
                textColor: AppColors.whiteColor,
                color: AppColors.blackColor,
                onPressed: () {
                  // context.push('/enableLocation');

                },

              ),
            ),
          ],
        ),
      ),


      // bottomNavigationBar:
    );
  }



  Widget _buildTermsAndConditionsCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 24,
          height: 24,
          child: Checkbox(
            value: _agreeToTerms,
            onChanged: (bool? value) {
              setState(() {
                _agreeToTerms = value!;
              });
            },
            activeColor: AppColors.primaryColor,
            // Light blue
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4)),
            side: const BorderSide(color: Colors.grey),
          ),
        ),
        const SizedBox(width: 8),
        const  Expanded(
          child: Text.rich(
            TextSpan(
              text:
              'I confirm that I have read and accept the Payment ',
              style: TextStyle(fontSize: 12.0),
              children: [
                TextSpan(
                  text: 'Terms & Conditions',
                  style: TextStyle(
                    color: Colors.blue,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

}
